<?php
/**
 * UTF-8 Test Suite for Laravel Developer Toolkit
 * Comprehensive testing of UTF-8 character encoding support
 */

// Force UTF-8 encoding
header('Content-Type: text/html; charset=UTF-8');
ini_set('default_charset', 'UTF-8');
mb_internal_encoding('UTF-8');
mb_http_output('UTF-8');

class UTF8TestSuite {
    
    private $test_results = [];
    
    public function runAllTests() {
        echo $this->renderHeader();
        
        // Test 1: Basic UTF-8 Support
        $this->testBasicUTF8Support();
        
        // Test 2: Encrypted File UTF-8 Handling
        $this->testEncryptedFileUTF8();
        
        // Test 3: Special Characters
        $this->testSpecialCharacters();
        
        // Test 4: Multi-byte Characters
        $this->testMultibyteCharacters();
        
        // Test 5: Encoding Conversion
        $this->testEncodingConversion();
        
        // Test 6: File Operations
        $this->testFileOperations();
        
        // Display results
        $this->displayResults();
        
        echo $this->renderFooter();
    }
    
    private function testBasicUTF8Support() {
        $this->addTest("Basic UTF-8 Support", function() {
            $utf8_string = "Hello 世界 🌍 Café naïve résumé";
            
            $checks = [
                'mb_check_encoding' => mb_check_encoding($utf8_string, 'UTF-8'),
                'mb_strlen' => mb_strlen($utf8_string, 'UTF-8') > 0,
                'mb_internal_encoding' => mb_internal_encoding() === 'UTF-8',
                'default_charset' => ini_get('default_charset') === 'UTF-8'
            ];
            
            return array_sum($checks) === count($checks);
        });
    }
    
    private function testEncryptedFileUTF8() {
        $this->addTest("Encrypted File UTF-8 Handling", function() {
            $files_to_test = [
                'working_encrypted_laravel_developer_toolkit.php',
                'fixed_utf8_encrypted_laravel_toolkit.php'
            ];
            
            $results = [];
            
            foreach ($files_to_test as $file) {
                if (file_exists($file)) {
                    $content = file_get_contents($file);
                    $encoding = mb_detect_encoding($content, ['UTF-8', 'ASCII', 'ISO-8859-1'], true);
                    $results[$file] = [
                        'exists' => true,
                        'encoding' => $encoding,
                        'utf8_valid' => mb_check_encoding($content, 'UTF-8')
                    ];
                } else {
                    $results[$file] = ['exists' => false];
                }
            }
            
            return !empty($results);
        });
    }
    
    private function testSpecialCharacters() {
        $this->addTest("Special Characters Support", function() {
            $special_chars = [
                'Accented' => 'àáâãäåæçèéêëìíîïñòóôõöøùúûüý',
                'Symbols' => '©®™€£¥§¶†‡•…‰′″‹›«»',
                'Math' => '±×÷∞≠≤≥∑∏∫√∂∆',
                'Arrows' => '←→↑↓↔↕⇐⇒⇑⇓⇔⇕',
                'Emoji' => '😀😃😄😁😆😅😂🤣😊😇🙂🙃😉😌😍🥰😘'
            ];
            
            $all_valid = true;
            foreach ($special_chars as $category => $chars) {
                if (!mb_check_encoding($chars, 'UTF-8')) {
                    $all_valid = false;
                    break;
                }
            }
            
            return $all_valid;
        });
    }
    
    private function testMultibyteCharacters() {
        $this->addTest("Multi-byte Characters", function() {
            $multibyte_strings = [
                'Chinese' => '你好世界',
                'Japanese' => 'こんにちは世界',
                'Korean' => '안녕하세요 세계',
                'Arabic' => 'مرحبا بالعالم',
                'Russian' => 'Привет мир',
                'Thai' => 'สวัสดีชาวโลก'
            ];
            
            $all_valid = true;
            foreach ($multibyte_strings as $lang => $text) {
                if (!mb_check_encoding($text, 'UTF-8') || mb_strlen($text, 'UTF-8') === 0) {
                    $all_valid = false;
                    break;
                }
            }
            
            return $all_valid;
        });
    }
    
    private function testEncodingConversion() {
        $this->addTest("Encoding Conversion", function() {
            $test_string = "Test string with special chars: café naïve résumé";
            
            // Test conversion from different encodings
            $iso_string = mb_convert_encoding($test_string, 'ISO-8859-1', 'UTF-8');
            $back_to_utf8 = mb_convert_encoding($iso_string, 'UTF-8', 'ISO-8859-1');
            
            return $back_to_utf8 === $test_string;
        });
    }
    
    private function testFileOperations() {
        $this->addTest("File Operations with UTF-8", function() {
            $test_content = "UTF-8 test content: 你好 🌍 café";
            $test_file = 'utf8_test_temp.txt';
            
            // Write UTF-8 content
            $write_success = file_put_contents($test_file, $test_content);
            
            if ($write_success === false) {
                return false;
            }
            
            // Read back and verify
            $read_content = file_get_contents($test_file);
            $encoding_valid = mb_check_encoding($read_content, 'UTF-8');
            $content_match = $read_content === $test_content;
            
            // Clean up
            if (file_exists($test_file)) {
                unlink($test_file);
            }
            
            return $encoding_valid && $content_match;
        });
    }
    
    private function addTest($name, $test_function) {
        try {
            $start_time = microtime(true);
            $result = $test_function();
            $end_time = microtime(true);
            $execution_time = round(($end_time - $start_time) * 1000, 2);
            
            $this->test_results[] = [
                'name' => $name,
                'passed' => $result,
                'execution_time' => $execution_time,
                'error' => null
            ];
        } catch (Exception $e) {
            $this->test_results[] = [
                'name' => $name,
                'passed' => false,
                'execution_time' => 0,
                'error' => $e->getMessage()
            ];
        }
    }
    
    private function displayResults() {
        echo "<div class='section'><h2>📊 Test Results Summary</h2>";
        
        $total_tests = count($this->test_results);
        $passed_tests = array_sum(array_column($this->test_results, 'passed'));
        $success_rate = $total_tests > 0 ? round(($passed_tests / $total_tests) * 100, 1) : 0;
        
        echo "<div class='stats'>";
        echo "<div class='stat-item'><span class='stat-number'>$total_tests</span><span class='stat-label'>Total Tests</span></div>";
        echo "<div class='stat-item'><span class='stat-number'>$passed_tests</span><span class='stat-label'>Passed</span></div>";
        echo "<div class='stat-item'><span class='stat-number'>" . ($total_tests - $passed_tests) . "</span><span class='stat-label'>Failed</span></div>";
        echo "<div class='stat-item'><span class='stat-number'>$success_rate%</span><span class='stat-label'>Success Rate</span></div>";
        echo "</div>";
        
        echo "<div class='progress'><div class='progress-bar' style='width: $success_rate%'></div></div>";
        
        echo "<div class='test-details'>";
        foreach ($this->test_results as $test) {
            $status_class = $test['passed'] ? 'success' : 'error';
            $status_icon = $test['passed'] ? '✅' : '❌';
            
            echo "<div class='test-result $status_class'>";
            echo "<div class='test-header'>";
            echo "<span class='test-icon'>$status_icon</span>";
            echo "<span class='test-name'>{$test['name']}</span>";
            echo "<span class='test-time'>{$test['execution_time']}ms</span>";
            echo "</div>";
            
            if ($test['error']) {
                echo "<div class='test-error'>Error: {$test['error']}</div>";
            }
            
            echo "</div>";
        }
        echo "</div>";
        
        echo "</div>";
    }
    
    private function renderHeader() {
        return "
        <!DOCTYPE html>
        <html lang='en'>
        <head>
            <meta charset='UTF-8'>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <title>UTF-8 Test Suite</title>
            <style>
                body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%); min-height: 100vh; }
                .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
                .header { text-align: center; margin-bottom: 30px; }
                .header h1 { color: #333; margin-bottom: 10px; }
                .section { margin: 25px 0; padding: 20px; border: 1px solid #e9ecef; border-radius: 10px; background: #f8f9fa; }
                .section h2 { color: #495057; margin-top: 0; }
                .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin: 20px 0; }
                .stat-item { text-align: center; padding: 15px; background: white; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
                .stat-number { display: block; font-size: 24px; font-weight: bold; color: #0984e3; }
                .stat-label { display: block; font-size: 12px; color: #6c757d; margin-top: 5px; }
                .progress { width: 100%; height: 20px; background: #e9ecef; border-radius: 10px; overflow: hidden; margin: 20px 0; }
                .progress-bar { height: 100%; background: linear-gradient(90deg, #00b894, #00cec9); transition: width 0.5s ease; }
                .test-details { margin-top: 20px; }
                .test-result { margin: 10px 0; padding: 15px; border-radius: 8px; border-left: 4px solid; }
                .test-result.success { background: #d4edda; border-left-color: #28a745; }
                .test-result.error { background: #f8d7da; border-left-color: #dc3545; }
                .test-header { display: flex; justify-content: space-between; align-items: center; }
                .test-name { font-weight: 500; }
                .test-time { font-size: 12px; color: #6c757d; }
                .test-error { margin-top: 10px; font-size: 14px; color: #721c24; }
            </style>
        </head>
        <body>
        <div class='container'>
            <div class='header'>
                <h1>🧪 UTF-8 Test Suite</h1>
                <p>Comprehensive testing of UTF-8 character encoding support in Laravel Developer Toolkit</p>
            </div>
        ";
    }
    
    private function renderFooter() {
        return "
        </div>
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                console.log('UTF-8 Test Suite completed');
                
                // Animate progress bar
                const progressBar = document.querySelector('.progress-bar');
                if (progressBar) {
                    const width = progressBar.style.width;
                    progressBar.style.width = '0%';
                    setTimeout(() => {
                        progressBar.style.width = width;
                    }, 500);
                }
                
                // Add test result animations
                const testResults = document.querySelectorAll('.test-result');
                testResults.forEach((result, index) => {
                    result.style.opacity = '0';
                    result.style.transform = 'translateY(20px)';
                    setTimeout(() => {
                        result.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
                        result.style.opacity = '1';
                        result.style.transform = 'translateY(0)';
                    }, index * 100);
                });
            });
        </script>
        </body>
        </html>";
    }
}

// Run the test suite
$testSuite = new UTF8TestSuite();
$testSuite->runAllTests();
?>
