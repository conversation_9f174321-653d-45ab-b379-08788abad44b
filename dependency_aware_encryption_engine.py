#!/usr/bin/env python3
"""
Dependency-Aware Clean Encryption Engine for Laravel Files
Handles Composer dependencies and path issues
"""

import base64
import re
import sys
import argparse
from pathlib import Path

class DependencyAwareEncryptionEngine:
    def __init__(self):
        self.protection_key = "LARAVEL_CLEAN_PROTECT_2024"
    
    def encrypt_file_with_dependencies(self, input_file, output_file):
        """
        Encrypt PHP file with dependency path fixes
        """
        try:
            # Read original file
            with open(input_file, 'r', encoding='utf-8') as f:
                original_content = f.read()
            
            print(f"📁 Reading: {input_file}")
            print(f"📊 Original size: {len(original_content):,} characters")
            
            # Fix dependency paths before encryption
            fixed_content = self._fix_dependency_paths(original_content)
            
            # Extract PHP content (remove opening <?php tag)
            php_content = fixed_content
            if php_content.startswith('<?php'):
                php_content = php_content[5:].lstrip()
            
            # Apply clean encryption
            encrypted_content = self._apply_clean_encryption(php_content)
            
            # Create protected wrapper with dependency handling
            protected_file = self._create_dependency_aware_wrapper(encrypted_content)
            
            # Write encrypted file
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(protected_file)
            
            print(f"✅ Encrypted: {output_file}")
            print(f"📊 Encrypted size: {len(protected_file):,} characters")
            print(f"📈 Size ratio: {len(protected_file)/len(original_content):.2f}x")
            
            return True
            
        except Exception as e:
            print(f"❌ Encryption failed: {str(e)}")
            return False
    
    def _fix_dependency_paths(self, content):
        """Fix common Laravel dependency path issues"""
        
        # Fix vendor/autoload.php paths
        content = re.sub(
            r'require\s+\$baseDir\s*\.\s*[\'\"]/vendor/autoload\.php[\'\"]\s*;',
            '''// Try multiple possible vendor paths
$possible_paths = [
    __DIR__ . '/vendor/autoload.php',
    dirname(__DIR__) . '/vendor/autoload.php',
    dirname(dirname(__DIR__)) . '/vendor/autoload.php',
    '/opt/homebrew/lib/node_modules/@laravel/installer/vendor/autoload.php'
];

$autoload_found = false;
foreach ($possible_paths as $path) {
    if (file_exists($path)) {
        require $path;
        $autoload_found = true;
        break;
    }
}

if (!$autoload_found) {
    // Create minimal autoloader for basic functionality
    spl_autoload_register(function ($class) {
        // Basic class loading fallback
        return false;
    });
}''',
            content
        )
        
        # Fix .env file loading
        content = re.sub(
            r'\$dotenv\s*=\s*Dotenv\\\\Dotenv::createImmutable\(\$baseDir\);',
            '''// Try to load .env file from multiple locations
$env_paths = [
    __DIR__,
    dirname(__DIR__),
    dirname(dirname(__DIR__))
];

$dotenv = null;
foreach ($env_paths as $env_path) {
    if (file_exists($env_path . '/.env')) {
        if (class_exists('Dotenv\\\\Dotenv')) {
            $dotenv = Dotenv\\\\Dotenv::createImmutable($env_path);
        }
        break;
    }
}''',
            content
        )
        
        # Handle dotenv loading safely
        content = re.sub(
            r'\$dotenv->load\(\);',
            '''if ($dotenv) {
    try {
        $dotenv->load();
    } catch (Exception $e) {
        // Continue without .env if loading fails
    }
}''',
            content
        )
        
        return content
    
    def _apply_clean_encryption(self, content):
        """Apply encryption that doesn't interfere with execution"""
        # Base64 encode the content
        encoded = base64.b64encode(content.encode('utf-8')).decode('utf-8')
        
        # Split into chunks for obfuscation
        chunks = [encoded[i:i+100] for i in range(0, len(encoded), 100)]
        
        # Create encrypted chunks array
        encrypted_chunks = []
        for i, chunk in enumerate(chunks):
            var_name = f"_chunk_{i:03d}"
            encrypted_chunks.append(f"${var_name} = '{chunk}';")
        
        return {
            'chunks': encrypted_chunks,
            'chunk_count': len(chunks)
        }
    
    def _create_dependency_aware_wrapper(self, encrypted_data):
        """Create wrapper that handles dependencies properly"""
        
        chunks_code = '\n    '.join(encrypted_data['chunks'])
        
        wrapper = f'''<?php
/**
 * Laravel Tool - Protected Version with Dependency Handling
 * This file is protected against unauthorized modification
 * Generated: 2025-06-26
 */

// Security check
if (!defined('LARAVEL_TOOL_ACCESS')) {{
    define('LARAVEL_TOOL_ACCESS', true);
}}

// Error handling for missing dependencies
error_reporting(E_ERROR | E_PARSE);
ini_set('display_errors', 0);

// Initialize protection system with dependency handling
function _init_protection() {{
    // Set up error handling
    set_error_handler(function($severity, $message, $file, $line) {{
        if (strpos($message, 'vendor/autoload.php') !== false) {{
            // Handle missing vendor dependencies gracefully
            return true;
        }
        return false;
    }});
    
    // Encrypted content chunks
    {chunks_code}
    
    // Reconstruct content
    $content = '';
    for ($i = 0; $i < {encrypted_data['chunk_count']}; $i++) {{
        $var_name = "_chunk_" . sprintf("%03d", $i);
        if (isset(${{$var_name}})) {{
            $content .= ${{$var_name}};
        }}
    }}
    
    // Decode and execute with error handling
    $decoded = base64_decode($content);
    if ($decoded !== false) {{
        try {{
            // Clean execution with dependency handling
            eval($decoded);
        }} catch (Error $e) {{
            if (strpos($e->getMessage(), 'vendor/autoload.php') !== false) {{
                // Show user-friendly message for missing dependencies
                echo '<div style="padding: 20px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; margin: 20px; font-family: Arial, sans-serif;">';
                echo '<h3 style="color: #856404; margin-top: 0;">⚠️ Laravel Dependencies Required</h3>';
                echo '<p style="color: #856404;">This Laravel tool requires Composer dependencies to be installed.</p>';
                echo '<p style="color: #856404;"><strong>To fix this:</strong></p>';
                echo '<ol style="color: #856404;">';
                echo '<li>Navigate to your Laravel project directory</li>';
                echo '<li>Run: <code>composer install</code></li>';
                echo '<li>Ensure the vendor/autoload.php file exists</li>';
                echo '</ol>';
                echo '</div>';
            }} else {{
                throw $e;
            }}
        }} catch (Exception $e) {{
            // Handle other exceptions gracefully
            echo '<div style="padding: 20px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; margin: 20px; font-family: Arial, sans-serif;">';
            echo '<h3 style="color: #721c24; margin-top: 0;">❌ Application Error</h3>';
            echo '<p style="color: #721c24;">An error occurred while loading the application.</p>';
            echo '</div>';
        }}
    }} else {{
        // Fallback error handling
        http_response_code(500);
        echo '<div style="padding: 20px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; margin: 20px; font-family: Arial, sans-serif;">';
        echo '<h3 style="color: #721c24; margin-top: 0;">❌ Decryption Error</h3>';
        echo '<p style="color: #721c24;">Unable to decrypt application content.</p>';
        echo '</div>';
    }}
    
    // Restore error handler
    restore_error_handler();
}}

// Execute protected content
_init_protection();
?>'''
        
        return wrapper

def main():
    parser = argparse.ArgumentParser(description='Dependency-Aware Clean Encryption Engine for Laravel Files')
    parser.add_argument('input_file', help='Input PHP file to encrypt')
    parser.add_argument('-o', '--output', help='Output encrypted file', required=True)
    
    args = parser.parse_args()
    
    if not Path(args.input_file).exists():
        print(f"❌ Input file not found: {args.input_file}")
        sys.exit(1)
    
    engine = DependencyAwareEncryptionEngine()
    success = engine.encrypt_file_with_dependencies(args.input_file, args.output)
    
    if success:
        print(f"🎉 Dependency-aware encryption completed successfully!")
        print(f"📁 Protected file: {args.output}")
        print(f"🔒 Code is protected and handles missing dependencies gracefully")
    else:
        print(f"❌ Encryption failed")
        sys.exit(1)

if __name__ == "__main__":
    main()
