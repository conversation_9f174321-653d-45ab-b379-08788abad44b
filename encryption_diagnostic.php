<?php
/**
 * <PERSON><PERSON> Developer Toolkit - Encryption Diagnostic Tool
 * This script diagnoses and fixes UTF-8 encoding issues in the encrypted file
 */

// Set UTF-8 encoding
header('Content-Type: text/html; charset=UTF-8');
ini_set('default_charset', 'UTF-8');
mb_internal_encoding('UTF-8');

echo "<!DOCTYPE html>\n";
echo "<html lang='en'>\n";
echo "<head>\n";
echo "    <meta charset='UTF-8'>\n";
echo "    <meta name='viewport' content='width=device-width, initial-scale=1.0'>\n";
echo "    <title>Encryption Diagnostic Tool</title>\n";
echo "    <style>\n";
echo "        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }\n";
echo "        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n";
echo "        .header { text-align: center; margin-bottom: 30px; }\n";
echo "        .status { padding: 15px; margin: 10px 0; border-radius: 5px; }\n";
echo "        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }\n";
echo "        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }\n";
echo "        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }\n";
echo "        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }\n";
echo "        .code-block { background: #f8f9fa; border: 1px solid #e9ecef; padding: 15px; border-radius: 5px; font-family: monospace; white-space: pre-wrap; max-height: 300px; overflow-y: auto; }\n";
echo "        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }\n";
echo "        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }\n";
echo "        .btn:hover { background: #0056b3; }\n";
echo "    </style>\n";
echo "</head>\n";
echo "<body>\n";

echo "<div class='container'>\n";
echo "    <div class='header'>\n";
echo "        <h1>🔍 Encryption Diagnostic Tool</h1>\n";
echo "        <p>Analyzing and fixing UTF-8 encoding issues in the Laravel Developer Toolkit</p>\n";
echo "    </div>\n";

// Step 1: Load and analyze the encrypted file
echo "    <div class='section'>\n";
echo "        <h2>📁 File Analysis</h2>\n";

$encrypted_file = 'working_encrypted_laravel_developer_toolkit.php';

if (!file_exists($encrypted_file)) {
    echo "        <div class='status error'>❌ Encrypted file not found: $encrypted_file</div>\n";
    exit;
}

echo "        <div class='status success'>✅ Encrypted file found: $encrypted_file</div>\n";

// Read the file content
$file_content = file_get_contents($encrypted_file);
$file_size = filesize($encrypted_file);
$encoding = mb_detect_encoding($file_content, ['UTF-8', 'ASCII', 'ISO-8859-1'], true);

echo "        <div class='status info'>📊 File size: " . number_format($file_size) . " bytes</div>\n";
echo "        <div class='status info'>🔤 Detected encoding: $encoding</div>\n";

// Step 2: Extract and test base64 variables
echo "    </div>\n";
echo "    <div class='section'>\n";
echo "        <h2>🔐 Base64 Variable Extraction</h2>\n";

// Include the encrypted file to get the variables
ob_start();
try {
    include $encrypted_file;
    $output = ob_get_contents();
} catch (Exception $e) {
    echo "        <div class='status error'>❌ Error including file: " . $e->getMessage() . "</div>\n";
    ob_end_clean();
    exit;
}
ob_end_clean();

// Check if variables are defined
$variables = [
    '$_SEBltMpK', '$_UWIeGqTm', '$_CXggbPsb', '$_VKquWwmY', '$_kbRiQUGw',
    '$_ZbPfwiHm', '$_uJZkktjr', '$_dULlHiiS', '$_nYdoFAmW', '$_rvpJLWzS',
    '$_alrkkJdz', '$_HuuWjvJy', '$_PHYhgYYL', '$_yfKcQQaf', '$_yJKihBOa',
    '$_LXrJhNeJ', '$_UdbYtiLn', '$_UNsaJufc', '$_QbymPLOT', '$_fobiNqic',
    '$_HLRbfPBH', '$_cfySrLkm', '$_CqavlQKw', '$_vhNRquJl', '$_PiGhzkhw',
    '$_tcjkysXo', '$_oieYqpsx', '$_plTrZNsd', '$_xdGHVOUR', '$_hLFnGVun',
    '$_kIScYtfe', '$_vvJibHoo', '$_utIfwbEQ'
];

$defined_vars = 0;
$total_base64_length = 0;

foreach ($variables as $var) {
    $var_name = substr($var, 1); // Remove the $
    if (isset($$var_name)) {
        $defined_vars++;
        $total_base64_length += strlen($$var_name);
    }
}

echo "        <div class='status info'>📝 Variables defined: $defined_vars/" . count($variables) . "</div>\n";
echo "        <div class='status info'>📏 Total base64 length: " . number_format($total_base64_length) . " characters</div>\n";

if ($defined_vars === count($variables)) {
    echo "        <div class='status success'>✅ All base64 variables are properly defined</div>\n";
} else {
    echo "        <div class='status error'>❌ Missing " . (count($variables) - $defined_vars) . " base64 variables</div>\n";
}

// Step 3: Concatenate and decode
echo "    </div>\n";
echo "    <div class='section'>\n";
echo "        <h2>🔗 Base64 Concatenation & Decoding</h2>\n";

// Concatenate all base64 strings
$concatenated_base64 = '';
foreach ($variables as $var) {
    $var_name = substr($var, 1);
    if (isset($$var_name)) {
        $concatenated_base64 .= $$var_name;
    }
}

echo "        <div class='status info'>🔗 Concatenated base64 length: " . number_format(strlen($concatenated_base64)) . " characters</div>\n";

// Test base64 validity
if (base64_encode(base64_decode($concatenated_base64)) === $concatenated_base64) {
    echo "        <div class='status success'>✅ Base64 string is valid</div>\n";
} else {
    echo "        <div class='status warning'>⚠️ Base64 string may have padding issues</div>\n";
}

// Decode the base64
$decoded_content = base64_decode($concatenated_base64);
$decoded_length = strlen($decoded_content);

echo "        <div class='status info'>📤 Decoded content length: " . number_format($decoded_length) . " bytes</div>\n";

// Check UTF-8 validity
if (mb_check_encoding($decoded_content, 'UTF-8')) {
    echo "        <div class='status success'>✅ Decoded content is valid UTF-8</div>\n";
} else {
    echo "        <div class='status error'>❌ Decoded content has UTF-8 encoding issues</div>\n";
}

// Step 4: Content analysis
echo "    </div>\n";
echo "    <div class='section'>\n";
echo "        <h2>📋 Content Analysis</h2>\n";

// Check if it's HTML content
if (strpos($decoded_content, '<!DOCTYPE html') !== false) {
    echo "        <div class='status success'>✅ Content appears to be HTML</div>\n";
} else {
    echo "        <div class='status warning'>⚠️ Content does not appear to be HTML</div>\n";
}

// Check for PHP tags
if (strpos($decoded_content, '<?php') !== false) {
    echo "        <div class='status info'>ℹ️ Content contains PHP code</div>\n";
}

// Show first 500 characters of decoded content
echo "        <h3>📝 Content Preview (first 500 characters):</h3>\n";
echo "        <div class='code-block'>" . htmlspecialchars(substr($decoded_content, 0, 500)) . "...</div>\n";

echo "    </div>\n";

// Step 5: Fix and create corrected version
echo "    <div class='section'>\n";
echo "        <h2>🔧 Fix & Create Corrected Version</h2>\n";

if (isset($_POST['create_fixed'])) {
    // Create a fixed version with proper UTF-8 support
    $fixed_content = createFixedEncryptedFile($decoded_content);
    
    $fixed_filename = 'fixed_encrypted_laravel_developer_toolkit.php';
    if (file_put_contents($fixed_filename, $fixed_content)) {
        echo "        <div class='status success'>✅ Fixed encrypted file created: $fixed_filename</div>\n";
        echo "        <a href='$fixed_filename' class='btn' target='_blank'>🚀 Test Fixed Version</a>\n";
    } else {
        echo "        <div class='status error'>❌ Failed to create fixed file</div>\n";
    }
} else {
    echo "        <form method='post'>\n";
    echo "            <button type='submit' name='create_fixed' class='btn'>🔧 Create Fixed Version with UTF-8 Support</button>\n";
    echo "        </form>\n";
}

echo "    </div>\n";

echo "</div>\n";
echo "</body>\n";
echo "</html>\n";

function createFixedEncryptedFile($original_content) {
    // Ensure UTF-8 encoding
    $content = mb_convert_encoding($original_content, 'UTF-8', 'auto');
    
    // Create new base64 encoded content
    $base64_content = base64_encode($content);
    
    // Split into chunks for obfuscation
    $chunk_size = 1000;
    $chunks = str_split($base64_content, $chunk_size);
    
    $php_code = "<?php\n";
    $php_code .= "// Anti-debugging and security measures\n";
    $php_code .= "if (function_exists('opcache_reset')) { opcache_reset(); }\n";
    $php_code .= "if (function_exists('apc_clear_cache')) { apc_clear_cache(); }\n";
    $php_code .= "error_reporting(0); ini_set('display_errors', 0);\n\n";
    
    // Generate random variable names and assign chunks
    $var_names = [];
    for ($i = 0; $i < count($chunks); $i++) {
        $var_name = '$_' . generateRandomString(9);
        $var_names[] = $var_name;
        $php_code .= "$var_name = \"" . $chunks[$i] . "\";\n";
    }
    
    $php_code .= "\n// Concatenate and decode with UTF-8 support\n";
    $php_code .= "mb_internal_encoding('UTF-8');\n";
    $php_code .= "\$_combined = " . implode(' . ', $var_names) . ";\n";
    $php_code .= "\$_decoded = base64_decode(\$_combined);\n";
    $php_code .= "\$_decoded = mb_convert_encoding(\$_decoded, 'UTF-8', 'auto');\n";
    $php_code .= "eval(\$_decoded);\n";
    $php_code .= "?>";
    
    return $php_code;
}

function generateRandomString($length = 10) {
    $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $randomString = '';
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, strlen($characters) - 1)];
    }
    return $randomString;
}
?>
