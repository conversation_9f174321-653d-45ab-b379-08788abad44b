#!/usr/bin/env python3
"""
Simple Dependency Fix Encryption Engine
"""

import base64
import sys
import argparse
from pathlib import Path

class SimpleDependencyFixEncryption:
    def encrypt_file(self, input_file, output_file):
        """Encrypt PHP file with dependency fixes"""
        try:
            # Read original file
            with open(input_file, 'r', encoding='utf-8') as f:
                original_content = f.read()
            
            print(f"📁 Reading: {input_file}")
            
            # Fix dependency paths
            fixed_content = self._fix_dependencies(original_content)
            
            # Extract PHP content
            php_content = fixed_content
            if php_content.startswith('<?php'):
                php_content = php_content[5:].lstrip()
            
            # Encode content
            encoded = base64.b64encode(php_content.encode('utf-8')).decode('utf-8')
            
            # Create wrapper
            wrapper = self._create_wrapper(encoded)
            
            # Write file
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(wrapper)
            
            print(f"✅ Encrypted: {output_file}")
            return True
            
        except Exception as e:
            print(f"❌ Error: {str(e)}")
            return False
    
    def _fix_dependencies(self, content):
        """Fix Laravel dependency paths"""
        # Replace problematic vendor path
        content = content.replace(
            "require $baseDir . '/vendor/autoload.php';",
            """// Try multiple vendor paths
$vendor_paths = [
    __DIR__ . '/vendor/autoload.php',
    dirname(__DIR__) . '/vendor/autoload.php',
    dirname(dirname(__DIR__)) . '/vendor/autoload.php'
];

$vendor_loaded = false;
foreach ($vendor_paths as $path) {
    if (file_exists($path)) {
        require $path;
        $vendor_loaded = true;
        break;
    }
}

if (!$vendor_loaded) {
    // Show dependency error
    echo '<div style="padding: 20px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; margin: 20px; font-family: Arial, sans-serif;">';
    echo '<h3 style="color: #856404;">⚠️ Laravel Dependencies Required</h3>';
    echo '<p style="color: #856404;">Please install Composer dependencies:</p>';
    echo '<pre style="background: #f8f9fa; padding: 10px; border-radius: 4px;">composer install</pre>';
    echo '</div>';
    exit;
}"""
        )
        
        # Fix dotenv loading
        content = content.replace(
            "$dotenv = Dotenv\\Dotenv::createImmutable($baseDir);",
            """$dotenv = null;
if (class_exists('Dotenv\\Dotenv')) {
    $dotenv = Dotenv\\Dotenv::createImmutable($baseDir);
}"""
        )
        
        content = content.replace(
            "$dotenv->load();",
            """if ($dotenv) {
    try {
        $dotenv->load();
    } catch (Exception $e) {
        // Continue without .env
    }
}"""
        )
        
        return content
    
    def _create_wrapper(self, encoded_content):
        """Create simple wrapper"""
        return f"""<?php
/**
 * Laravel Tool - Protected Version
 * Generated: 2025-06-26
 */

// Initialize protection
function _load_protected_content() {{
    $encoded = '{encoded_content}';
    $decoded = base64_decode($encoded);
    
    if ($decoded !== false) {{
        eval($decoded);
    }} else {{
        echo '<div style="padding: 20px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; margin: 20px;">';
        echo '<h3 style="color: #721c24;">❌ Decryption Error</h3>';
        echo '</div>';
    }}
}}

// Execute
_load_protected_content();
?>"""

def main():
    parser = argparse.ArgumentParser(description='Simple Dependency Fix Encryption')
    parser.add_argument('input_file', help='Input PHP file')
    parser.add_argument('-o', '--output', help='Output file', required=True)
    
    args = parser.parse_args()
    
    if not Path(args.input_file).exists():
        print(f"❌ File not found: {args.input_file}")
        sys.exit(1)
    
    engine = SimpleDependencyFixEncryption()
    if engine.encrypt_file(args.input_file, args.output):
        print("🎉 Success!")
    else:
        sys.exit(1)

if __name__ == "__main__":
    main()
