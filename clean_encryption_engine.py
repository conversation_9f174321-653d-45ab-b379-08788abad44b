#!/usr/bin/env python3
"""
Clean Encryption Engine for Laravel Files
Provides code protection while maintaining clean browser functionality
"""

import base64
import re
import sys
import argparse
from pathlib import Path

class CleanEncryptionEngine:
    def __init__(self):
        self.protection_key = "LARAVEL_CLEAN_PROTECT_2024"
    
    def encrypt_file_clean(self, input_file, output_file):
        """
        Encrypt PHP file with clean browser output
        - Protects source code when viewed directly
        - Maintains clean functionality in browser
        - No obfuscated output visible to users
        """
        try:
            # Read original file
            with open(input_file, 'r', encoding='utf-8') as f:
                original_content = f.read()
            
            print(f"📁 Reading: {input_file}")
            print(f"📊 Original size: {len(original_content):,} characters")
            
            # Extract PHP content (remove opening <?php tag)
            php_content = original_content
            if php_content.startswith('<?php'):
                php_content = php_content[5:].lstrip()
            
            # Apply clean encryption
            encrypted_content = self._apply_clean_encryption(php_content)
            
            # Create protected wrapper
            protected_file = self._create_clean_wrapper(encrypted_content)
            
            # Write encrypted file
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(protected_file)
            
            print(f"✅ Encrypted: {output_file}")
            print(f"📊 Encrypted size: {len(protected_file):,} characters")
            print(f"📈 Size ratio: {len(protected_file)/len(original_content):.2f}x")
            
            return True
            
        except Exception as e:
            print(f"❌ Encryption failed: {str(e)}")
            return False
    
    def _apply_clean_encryption(self, content):
        """Apply encryption that doesn't interfere with execution"""
        # Base64 encode the content
        encoded = base64.b64encode(content.encode('utf-8')).decode('utf-8')
        
        # Split into chunks for obfuscation
        chunks = [encoded[i:i+100] for i in range(0, len(encoded), 100)]
        
        # Create encrypted chunks array
        encrypted_chunks = []
        for i, chunk in enumerate(chunks):
            var_name = f"_chunk_{i:03d}"
            encrypted_chunks.append(f"${var_name} = '{chunk}';")
        
        return {
            'chunks': encrypted_chunks,
            'chunk_count': len(chunks)
        }
    
    def _create_clean_wrapper(self, encrypted_data):
        """Create wrapper that executes cleanly without showing obfuscated code"""
        
        chunks_code = '\n    '.join(encrypted_data['chunks'])
        
        wrapper = f'''<?php
/**
 * Laravel Tool - Protected Version
 * This file is protected against unauthorized modification
 * Generated: 2025-06-26
 */

// Security check
if (!defined('LARAVEL_TOOL_ACCESS')) {{
    define('LARAVEL_TOOL_ACCESS', true);
}}

// Initialize protection system
function _init_protection() {{
    // Encrypted content chunks
    {chunks_code}
    
    // Reconstruct content
    $content = '';
    for ($i = 0; $i < {encrypted_data['chunk_count']}; $i++) {{
        $var_name = "_chunk_" . sprintf("%03d", $i);
        if (isset(${{$var_name}})) {{
            $content .= ${{$var_name}};
        }}
    }}
    
    // Decode and execute
    $decoded = base64_decode($content);
    if ($decoded !== false) {{
        // Clean execution without output buffering issues
        eval($decoded);
    }} else {{
        // Fallback error handling
        http_response_code(500);
        exit('Application temporarily unavailable');
    }}
}}

// Execute protected content
_init_protection();
?>'''
        
        return wrapper

def main():
    parser = argparse.ArgumentParser(description='Clean Encryption Engine for Laravel Files')
    parser.add_argument('input_file', help='Input PHP file to encrypt')
    parser.add_argument('-o', '--output', help='Output encrypted file', required=True)
    
    args = parser.parse_args()
    
    if not Path(args.input_file).exists():
        print(f"❌ Input file not found: {args.input_file}")
        sys.exit(1)
    
    engine = CleanEncryptionEngine()
    success = engine.encrypt_file_clean(args.input_file, args.output)
    
    if success:
        print(f"🎉 Clean encryption completed successfully!")
        print(f"📁 Protected file: {args.output}")
        print(f"🔒 Code is protected but will execute cleanly in browser")
    else:
        print(f"❌ Encryption failed")
        sys.exit(1)

if __name__ == "__main__":
    main()
