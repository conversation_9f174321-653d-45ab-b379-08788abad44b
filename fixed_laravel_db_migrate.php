<?php
/**
 * <PERSON><PERSON> Tool - Protected Version
 * Generated: 2025-06-26
 */

// Initialize protection
function _load_protected_content() {
    $encoded = '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';
    $decoded = base64_decode($encoded);
    
    if ($decoded !== false) {
        eval($decoded);
    } else {
        echo '<div style="padding: 20px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; margin: 20px;">';
        echo '<h3 style="color: #721c24;">❌ Decryption Error</h3>';
        echo '</div>';
    }
}

// Execute
_load_protected_content();
?>