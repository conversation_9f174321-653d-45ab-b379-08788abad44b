# Example Laravel PHP Files

This directory contains sample Laravel PHP files that you can use to test the encryption functionality.

## Available Examples:

- **laravel_db_migrate.php** - Database migration tool for shared hosting
- **laravel_db_restore.php** - Database backup and restore utility  
- **laravel_developer_toolkit.php** - Development tools and utilities
- **laravel_npm_build.php** - NPM build automation script
- **laravel_permissions_fixer.php** - File permissions management
- **laravel_prod_error-fixer.php** - Production error handling
- **laravel_run_artisan.php** - Artisan command runner for shared hosting
- **laravel_symlink_creator.php** - Symbolic link management

## Usage:

1. Select any of these files in the main application
2. Configure your encryption settings
3. Generate encrypted versions for production use

⚠️ **Note**: These are example files. Always test encrypted files in a development environment before deploying to production.
