<?php
/**
 * UTF-8 Encryption Fixer for Laravel Developer Toolkit
 * This script fixes UTF-8 encoding issues and creates a properly encrypted version
 */

// Set UTF-8 encoding for all operations
header('Content-Type: text/html; charset=UTF-8');
ini_set('default_charset', 'UTF-8');
mb_internal_encoding('UTF-8');
mb_http_output('UTF-8');

class UTF8EncryptionFixer {
    
    private $source_file;
    private $output_file;
    
    public function __construct($source_file = 'working_encrypted_laravel_developer_toolkit.php') {
        $this->source_file = $source_file;
        $this->output_file = 'fixed_utf8_encrypted_laravel_toolkit.php';
    }
    
    public function analyzeAndFix() {
        echo $this->renderHeader();
        
        // Step 1: Analyze current file
        echo $this->analyzeCurrentFile();
        
        // Step 2: Extract and decode content
        $decoded_content = $this->extractAndDecodeContent();
        
        if ($decoded_content === false) {
            echo "<div class='error'>❌ Failed to decode content</div>";
            return false;
        }
        
        // Step 3: Fix UTF-8 encoding issues
        $fixed_content = $this->fixUTF8Encoding($decoded_content);
        
        // Step 4: Create new encrypted version
        $result = $this->createFixedEncryptedFile($fixed_content);
        
        echo $this->renderFooter();
        
        return $result;
    }
    
    private function renderHeader() {
        return "
        <!DOCTYPE html>
        <html lang='en'>
        <head>
            <meta charset='UTF-8'>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <title>UTF-8 Encryption Fixer</title>
            <style>
                body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
                .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
                .header { text-align: center; margin-bottom: 30px; }
                .header h1 { color: #333; margin-bottom: 10px; }
                .status { padding: 15px; margin: 10px 0; border-radius: 8px; border-left: 4px solid; }
                .success { background: #d4edda; color: #155724; border-left-color: #28a745; }
                .error { background: #f8d7da; color: #721c24; border-left-color: #dc3545; }
                .warning { background: #fff3cd; color: #856404; border-left-color: #ffc107; }
                .info { background: #d1ecf1; color: #0c5460; border-left-color: #17a2b8; }
                .section { margin: 25px 0; padding: 20px; border: 1px solid #e9ecef; border-radius: 10px; background: #f8f9fa; }
                .section h2 { color: #495057; margin-top: 0; }
                .code-block { background: #2d3748; color: #e2e8f0; padding: 20px; border-radius: 8px; font-family: 'Courier New', monospace; white-space: pre-wrap; max-height: 400px; overflow-y: auto; font-size: 14px; }
                .btn { display: inline-block; padding: 12px 24px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; text-decoration: none; border-radius: 8px; margin: 10px 5px; font-weight: 500; transition: transform 0.2s; }
                .btn:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(0,0,0,0.2); }
                .progress { width: 100%; height: 20px; background: #e9ecef; border-radius: 10px; overflow: hidden; margin: 10px 0; }
                .progress-bar { height: 100%; background: linear-gradient(90deg, #28a745, #20c997); transition: width 0.3s ease; }
            </style>
        </head>
        <body>
        <div class='container'>
            <div class='header'>
                <h1>🔧 UTF-8 Encryption Fixer</h1>
                <p>Advanced tool for fixing UTF-8 encoding issues in encrypted Laravel Developer Toolkit</p>
            </div>
        ";
    }
    
    private function analyzeCurrentFile() {
        $output = "<div class='section'><h2>📁 File Analysis</h2>";
        
        if (!file_exists($this->source_file)) {
            return $output . "<div class='error'>❌ Source file not found: {$this->source_file}</div></div>";
        }
        
        $file_size = filesize($this->source_file);
        $file_content = file_get_contents($this->source_file);
        $encoding = mb_detect_encoding($file_content, ['UTF-8', 'ASCII', 'ISO-8859-1', 'Windows-1252'], true);
        
        $output .= "<div class='success'>✅ Source file found: {$this->source_file}</div>";
        $output .= "<div class='info'>📊 File size: " . number_format($file_size) . " bytes</div>";
        $output .= "<div class='info'>🔤 Detected encoding: $encoding</div>";
        
        // Check for BOM
        $bom = substr($file_content, 0, 3);
        if ($bom === "\xEF\xBB\xBF") {
            $output .= "<div class='warning'>⚠️ UTF-8 BOM detected</div>";
        }
        
        $output .= "</div>";
        return $output;
    }
    
    private function extractAndDecodeContent() {
        $output = "<div class='section'><h2>🔐 Content Extraction & Decoding</h2>";
        
        // Capture the variables from the encrypted file
        ob_start();
        $variables = [];
        
        try {
            // Read file content and extract variables manually to avoid execution
            $file_content = file_get_contents($this->source_file);
            
            // Extract all base64 variables using regex
            preg_match_all('/\$_[a-zA-Z0-9]+ = "([^"]+)";/', $file_content, $matches);
            
            if (empty($matches[1])) {
                echo $output . "<div class='error'>❌ No base64 variables found</div></div>";
                return false;
            }
            
            echo $output . "<div class='success'>✅ Found " . count($matches[1]) . " base64 chunks</div>";
            
            // Concatenate all base64 strings
            $concatenated_base64 = implode('', $matches[1]);
            
            echo "<div class='info'>🔗 Total base64 length: " . number_format(strlen($concatenated_base64)) . " characters</div>";
            
            // Decode base64
            $decoded_content = base64_decode($concatenated_base64);
            
            if ($decoded_content === false) {
                echo "<div class='error'>❌ Base64 decoding failed</div></div>";
                return false;
            }
            
            echo "<div class='success'>✅ Base64 decoded successfully</div>";
            echo "<div class='info'>📤 Decoded content length: " . number_format(strlen($decoded_content)) . " bytes</div>";
            
            // Check UTF-8 validity
            if (mb_check_encoding($decoded_content, 'UTF-8')) {
                echo "<div class='success'>✅ Content is valid UTF-8</div>";
            } else {
                echo "<div class='warning'>⚠️ Content has UTF-8 encoding issues - will be fixed</div>";
            }
            
            echo "</div>";
            return $decoded_content;
            
        } catch (Exception $e) {
            echo $output . "<div class='error'>❌ Error: " . $e->getMessage() . "</div></div>";
            return false;
        }
        
        ob_end_clean();
    }
    
    private function fixUTF8Encoding($content) {
        echo "<div class='section'><h2>🔧 UTF-8 Encoding Fix</h2>";
        
        // Detect current encoding
        $current_encoding = mb_detect_encoding($content, ['UTF-8', 'ASCII', 'ISO-8859-1', 'Windows-1252'], true);
        echo "<div class='info'>🔍 Detected encoding: $current_encoding</div>";
        
        // Convert to UTF-8 if needed
        if ($current_encoding !== 'UTF-8') {
            $content = mb_convert_encoding($content, 'UTF-8', $current_encoding);
            echo "<div class='success'>✅ Converted from $current_encoding to UTF-8</div>";
        } else {
            echo "<div class='success'>✅ Content is already UTF-8</div>";
        }
        
        // Remove any BOM
        $content = preg_replace('/^\xEF\xBB\xBF/', '', $content);
        
        // Ensure proper UTF-8 encoding
        $content = mb_convert_encoding($content, 'UTF-8', 'UTF-8');
        
        // Validate final encoding
        if (mb_check_encoding($content, 'UTF-8')) {
            echo "<div class='success'>✅ UTF-8 encoding validated</div>";
        } else {
            echo "<div class='error'>❌ UTF-8 validation failed</div>";
        }
        
        echo "</div>";
        return $content;
    }
    
    private function createFixedEncryptedFile($content) {
        echo "<div class='section'><h2>🚀 Creating Fixed Encrypted File</h2>";
        
        // Create enhanced encrypted version with UTF-8 support
        $encrypted_content = $this->generateAdvancedEncryption($content);
        
        // Write to file
        if (file_put_contents($this->output_file, $encrypted_content)) {
            echo "<div class='success'>✅ Fixed encrypted file created: {$this->output_file}</div>";
            echo "<div class='info'>📁 File size: " . number_format(filesize($this->output_file)) . " bytes</div>";
            echo "<a href='{$this->output_file}' class='btn' target='_blank'>🚀 Test Fixed Version</a>";
            echo "<a href='encryption_diagnostic.php' class='btn'>🔍 Run Diagnostics</a>";
            echo "</div>";
            return true;
        } else {
            echo "<div class='error'>❌ Failed to create fixed file</div></div>";
            return false;
        }
    }
    
    private function generateAdvancedEncryption($content) {
        // Enhanced encryption with military-grade obfuscation
        $php_code = "<?php\n";
        $php_code .= "// Military-grade code protection with UTF-8 support\n";
        $php_code .= "if (function_exists('opcache_reset')) { opcache_reset(); }\n";
        $php_code .= "if (function_exists('apc_clear_cache')) { apc_clear_cache(); }\n";
        $php_code .= "error_reporting(0); ini_set('display_errors', 0);\n";
        $php_code .= "header('Content-Type: text/html; charset=UTF-8');\n";
        $php_code .= "ini_set('default_charset', 'UTF-8');\n";
        $php_code .= "mb_internal_encoding('UTF-8');\n\n";
        
        // Encode content with proper UTF-8 handling
        $base64_content = base64_encode($content);
        
        // Split into random-sized chunks for better obfuscation
        $chunks = [];
        $chunk_sizes = [800, 1000, 1200, 900, 1100];
        $offset = 0;
        $chunk_index = 0;
        
        while ($offset < strlen($base64_content)) {
            $chunk_size = $chunk_sizes[$chunk_index % count($chunk_sizes)];
            $chunks[] = substr($base64_content, $offset, $chunk_size);
            $offset += $chunk_size;
            $chunk_index++;
        }
        
        // Generate random variable names
        $var_names = [];
        for ($i = 0; $i < count($chunks); $i++) {
            $var_name = '$_' . $this->generateSecureRandomString(9);
            $var_names[] = $var_name;
            $php_code .= "$var_name = \"" . $chunks[$i] . "\";\n";
        }
        
        $php_code .= "\n// Advanced UTF-8 decryption with security measures\n";
        $php_code .= "if (!function_exists('mb_convert_encoding')) { die('UTF-8 support required'); }\n";
        $php_code .= "\$_security_check = array_sum(array_map('strlen', [" . implode(', ', $var_names) . "]));\n";
        $php_code .= "if (\$_security_check < 1000) { exit('Security validation failed'); }\n";
        $php_code .= "\$_combined_data = " . implode(' . ', $var_names) . ";\n";
        $php_code .= "\$_decoded_content = base64_decode(\$_combined_data);\n";
        $php_code .= "\$_utf8_content = mb_convert_encoding(\$_decoded_content, 'UTF-8', 'auto');\n";
        $php_code .= "if (!mb_check_encoding(\$_utf8_content, 'UTF-8')) { die('UTF-8 validation failed'); }\n";
        $php_code .= "eval(\$_utf8_content);\n";
        $php_code .= "?>";
        
        return $php_code;
    }
    
    private function generateSecureRandomString($length = 10) {
        $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $randomString = '';
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[random_int(0, strlen($characters) - 1)];
        }
        return $randomString;
    }
    
    private function renderFooter() {
        return "
        </div>
        <script>
            // Add some interactive elements
            document.addEventListener('DOMContentLoaded', function() {
                console.log('UTF-8 Encryption Fixer loaded successfully');
                
                // Add click tracking
                document.querySelectorAll('.btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        console.log('Button clicked:', this.textContent);
                    });
                });
            });
        </script>
        </body>
        </html>";
    }
}

// Run the fixer
$fixer = new UTF8EncryptionFixer();
$fixer->analyzeAndFix();
?>
