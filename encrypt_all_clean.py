#!/usr/bin/env python3
"""
Encrypt all Laravel files with clean encryption
"""

import subprocess
import sys
from pathlib import Path

# List of Laravel files to encrypt
laravel_files = [
    'laravel_db_migrate_tool.php',
    'laravel_db_migrate.php',
    'laravel_db_restore.php',
    'laravel_developer_toolkit.php',
    'laravel_npm_build.php',
    'laravel_permissions_fixer.php',
    'laravel_prod_error-fixer.php',
    'laravel_run_artisan.php',
    'laravel_symlink_creator.php'
]

def encrypt_file(input_file, output_file):
    """Encrypt a single file using clean encryption"""
    try:
        result = subprocess.run([
            'python3', 'clean_encryption_engine.py',
            input_file, '-o', output_file
        ], capture_output=True, text=True, check=True)
        
        print(f"✅ {input_file} -> {output_file}")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to encrypt {input_file}: {e}")
        return False
    except Exception as e:
        print(f"❌ Error encrypting {input_file}: {e}")
        return False

def main():
    print("🔒 Starting clean encryption of all Laravel files...")
    print("=" * 60)
    
    success_count = 0
    total_files = len(laravel_files)
    
    for input_file in laravel_files:
        if not Path(input_file).exists():
            print(f"⚠️ File not found: {input_file}")
            continue
            
        output_file = f"clean_encrypted_{input_file}"
        
        if encrypt_file(input_file, output_file):
            success_count += 1
    
    print("=" * 60)
    print(f"🎉 Encryption completed!")
    print(f"✅ Successfully encrypted: {success_count}/{total_files} files")
    
    if success_count == total_files:
        print("🚀 All files encrypted successfully with clean output!")
    else:
        print(f"⚠️ {total_files - success_count} files failed to encrypt")

if __name__ == "__main__":
    main()
