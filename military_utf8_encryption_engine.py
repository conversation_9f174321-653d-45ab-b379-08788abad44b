#!/usr/bin/env python3
"""
Military-Grade UTF-8 Encryption Engine
Enhanced encryption system with comprehensive UTF-8 character encoding support
Integrates with existing military code protection framework
"""

import os
import sys
import base64
import secrets
import string
import random
import hashlib
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from military_code_protector.core.custom_encryption import CustomEncryptionEngine
    from military_code_protector.core.enhanced_encryption import EnhancedEncryptionEngine
    from military_code_protector.core.browser_compatible_encryption import BrowserCompatibleEncryption
except ImportError:
    # Fallback if military_code_protector is not available
    CustomEncryptionEngine = None
    EnhancedEncryptionEngine = None
    BrowserCompatibleEncryption = None

class MilitaryUTF8EncryptionEngine:
    """
    Advanced UTF-8 encryption engine with military-grade protection.
    Ensures proper UTF-8 character encoding support throughout the encryption process.
    """
    
    def __init__(self):
        self.chunk_size = 1000
        self.encoding = 'utf-8'
        self.custom_encryption = CustomEncryptionEngine() if CustomEncryptionEngine else None
        self.enhanced_encryption = EnhancedEncryptionEngine() if EnhancedEncryptionEngine else None
        self.browser_encryption = BrowserCompatibleEncryption() if BrowserCompatibleEncryption else None
        
    def encrypt_file_with_utf8_support(self, input_path: str, output_path: Optional[str] = None, 
                                     encryption_level: str = 'advanced') -> bool:
        """
        Encrypt a file with comprehensive UTF-8 support.
        
        Args:
            input_path: Path to the input file
            output_path: Path for the encrypted output file
            encryption_level: Level of encryption ('basic', 'standard', 'advanced', 'maximum')
            
        Returns:
            bool: True if encryption successful, False otherwise
        """
        try:
            # Read file with UTF-8 encoding
            with open(input_path, 'r', encoding='utf-8', errors='replace') as f:
                original_content = f.read()
            
            # Validate UTF-8 content
            if not self._validate_utf8_content(original_content):
                print(f"Warning: Input file contains non-UTF-8 content. Converting...")
                original_content = self._fix_utf8_content(original_content)
            
            # Apply encryption based on level
            encrypted_content = self._apply_utf8_encryption(original_content, encryption_level)
            
            # Determine output path
            if output_path is None:
                base_name = os.path.splitext(os.path.basename(input_path))[0]
                output_path = f"encrypted_utf8_{base_name}.php"
            
            # Write encrypted file with UTF-8 support
            with open(output_path, 'w', encoding='utf-8', newline='') as f:
                f.write(encrypted_content)
            
            print(f"✅ File encrypted successfully: {output_path}")
            print(f"📊 Original size: {len(original_content)} characters")
            print(f"📊 Encrypted size: {len(encrypted_content)} characters")
            print(f"🔒 Encryption level: {encryption_level}")
            
            return True
            
        except Exception as e:
            print(f"❌ Encryption failed: {str(e)}")
            return False
    
    def _validate_utf8_content(self, content: str) -> bool:
        """Validate that content is properly UTF-8 encoded."""
        try:
            content.encode('utf-8')
            return True
        except UnicodeEncodeError:
            return False
    
    def _fix_utf8_content(self, content: str) -> str:
        """Fix UTF-8 encoding issues in content."""
        try:
            # Try to encode and decode to fix encoding issues
            fixed_content = content.encode('utf-8', errors='replace').decode('utf-8')
            return fixed_content
        except Exception:
            # Fallback: remove non-UTF-8 characters
            return ''.join(char for char in content if ord(char) < 128 or char.isalnum())
    
    def _apply_utf8_encryption(self, content: str, level: str) -> str:
        """Apply UTF-8-aware encryption based on the specified level."""
        
        if level == 'maximum' and self.enhanced_encryption:
            return self._apply_maximum_utf8_encryption(content)
        elif level == 'advanced' and self.custom_encryption:
            return self._apply_advanced_utf8_encryption(content)
        elif level == 'standard':
            return self._apply_standard_utf8_encryption(content)
        else:
            return self._apply_basic_utf8_encryption(content)
    
    def _apply_maximum_utf8_encryption(self, content: str) -> str:
        """Apply maximum security UTF-8 encryption using enhanced encryption engine."""
        try:
            config = {
                'level': 'advanced',
                'multiLayer': True,
                'dynamicKeys': True,
                'antiDebugging': True,
                'integrityCheck': True,
                'utf8Support': True
            }
            return self.enhanced_encryption.encrypt_content(content, config)
        except Exception as e:
            print(f"Warning: Maximum encryption failed, falling back to advanced: {e}")
            return self._apply_advanced_utf8_encryption(content)
    
    def _apply_advanced_utf8_encryption(self, content: str) -> str:
        """Apply advanced UTF-8 encryption with multiple layers."""
        try:
            if self.custom_encryption:
                config = {
                    'level': 'advanced',
                    'multiLayer': True,
                    'utf8Support': True
                }
                return self.custom_encryption.create_advanced_encryption(content, config)
            else:
                return self._apply_standard_utf8_encryption(content)
        except Exception as e:
            print(f"Warning: Advanced encryption failed, falling back to standard: {e}")
            return self._apply_standard_utf8_encryption(content)
    
    def _apply_standard_utf8_encryption(self, content: str) -> str:
        """Apply standard UTF-8 encryption with moderate obfuscation."""
        # Remove original <?php tag if present
        if content.startswith('<?php'):
            content = content[5:].lstrip()
        
        # Layer 1: UTF-8 aware base64 encoding
        utf8_bytes = content.encode('utf-8')
        base64_content = base64.b64encode(utf8_bytes).decode('ascii')
        
        # Layer 2: XOR encryption with UTF-8 key
        xor_key = self._generate_utf8_key(32)
        xor_encrypted = self._xor_encrypt_utf8(base64_content, xor_key)
        
        # Layer 3: Base64 encode the XOR result
        final_base64 = base64.b64encode(xor_encrypted.encode('utf-8')).decode('ascii')
        
        # Split into chunks for obfuscation
        chunks = self._split_into_chunks(final_base64)
        var_names = [self._generate_random_var() for _ in range(len(chunks))]
        
        # Create encrypted PHP file
        return self._create_standard_utf8_decoder(chunks, var_names, xor_key)
    
    def _apply_basic_utf8_encryption(self, content: str) -> str:
        """Apply basic UTF-8 encryption with simple obfuscation."""
        # Remove original <?php tag if present
        if content.startswith('<?php'):
            content = content[5:].lstrip()
        
        # Simple UTF-8 aware base64 encoding
        utf8_bytes = content.encode('utf-8')
        base64_content = base64.b64encode(utf8_bytes).decode('ascii')
        
        # Split into chunks
        chunks = self._split_into_chunks(base64_content)
        var_names = [self._generate_random_var() for _ in range(len(chunks))]
        
        # Create encrypted PHP file
        return self._create_basic_utf8_decoder(chunks, var_names)
    
    def _generate_utf8_key(self, length: int) -> str:
        """Generate a UTF-8 compatible encryption key."""
        # Use ASCII characters for key to ensure compatibility
        chars = string.ascii_letters + string.digits
        return ''.join(secrets.choice(chars) for _ in range(length))
    
    def _xor_encrypt_utf8(self, data: str, key: str) -> str:
        """XOR encrypt data with UTF-8 support."""
        result = []
        for i, char in enumerate(data):
            key_char = key[i % len(key)]
            encrypted_char = chr(ord(char) ^ ord(key_char))
            result.append(encrypted_char)
        return ''.join(result)
    
    def _split_into_chunks(self, content: str) -> List[str]:
        """Split content into random-sized chunks for obfuscation."""
        chunks = []
        chunk_sizes = [800, 1000, 1200, 900, 1100]
        offset = 0
        chunk_index = 0
        
        while offset < len(content):
            chunk_size = chunk_sizes[chunk_index % len(chunk_sizes)]
            chunks.append(content[offset:offset + chunk_size])
            offset += chunk_size
            chunk_index += 1
        
        return chunks
    
    def _generate_random_var(self) -> str:
        """Generate a random variable name."""
        chars = string.ascii_letters
        return '$_' + ''.join(secrets.choice(chars) for _ in range(9))
    
    def _create_basic_utf8_decoder(self, chunks: List[str], var_names: List[str]) -> str:
        """Create basic UTF-8 decoder PHP code."""
        php_code = "<?php\n"
        php_code += "// Military-grade code protection with UTF-8 support\n"
        php_code += "error_reporting(0); ini_set('display_errors', 0);\n"
        php_code += "header('Content-Type: text/html; charset=UTF-8');\n"
        php_code += "ini_set('default_charset', 'UTF-8');\n"
        php_code += "mb_internal_encoding('UTF-8');\n\n"
        
        # Add anti-debugging
        php_code += "// Anti-debugging measures\n"
        php_code += "if (function_exists('opcache_reset')) { opcache_reset(); }\n"
        php_code += "if (function_exists('apc_clear_cache')) { apc_clear_cache(); }\n"
        php_code += "if (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) { exit('Access denied'); }\n\n"
        
        # Define variables
        for i, (var_name, chunk) in enumerate(zip(var_names, chunks)):
            php_code += f"{var_name} = \"{chunk}\";\n"
        
        php_code += "\n// UTF-8 aware decryption\n"
        php_code += "if (!function_exists('mb_convert_encoding')) { die('UTF-8 support required'); }\n"
        php_code += f"$_combined = {' . '.join(var_names)};\n"
        php_code += "$_decoded = base64_decode($_combined);\n"
        php_code += "$_utf8_content = mb_convert_encoding($_decoded, 'UTF-8', 'auto');\n"
        php_code += "if (!mb_check_encoding($_utf8_content, 'UTF-8')) { die('UTF-8 validation failed'); }\n"
        php_code += "eval($_utf8_content);\n"
        php_code += "?>"
        
        return php_code
    
    def _create_standard_utf8_decoder(self, chunks: List[str], var_names: List[str], xor_key: str) -> str:
        """Create standard UTF-8 decoder PHP code with XOR decryption."""
        php_code = "<?php\n"
        php_code += "// Military-grade code protection with UTF-8 support\n"
        php_code += "error_reporting(0); ini_set('display_errors', 0);\n"
        php_code += "header('Content-Type: text/html; charset=UTF-8');\n"
        php_code += "ini_set('default_charset', 'UTF-8');\n"
        php_code += "mb_internal_encoding('UTF-8');\n\n"
        
        # Enhanced anti-debugging
        php_code += "// Enhanced anti-debugging measures\n"
        php_code += "if (function_exists('opcache_reset')) { opcache_reset(); }\n"
        php_code += "if (function_exists('apc_clear_cache')) { apc_clear_cache(); }\n"
        php_code += "if (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) { exit('Access denied'); }\n"
        php_code += "if (function_exists('xdebug_is_enabled') && xdebug_is_enabled()) { exit('Debug detected'); }\n\n"
        
        # Define variables
        for i, (var_name, chunk) in enumerate(zip(var_names, chunks)):
            php_code += f"{var_name} = \"{chunk}\";\n"
        
        # Add XOR key (obfuscated)
        obfuscated_key = base64.b64encode(xor_key.encode('utf-8')).decode('ascii')
        php_code += f"\n$_key = base64_decode('{obfuscated_key}');\n"
        
        php_code += "\n// UTF-8 aware decryption with XOR\n"
        php_code += "if (!function_exists('mb_convert_encoding')) { die('UTF-8 support required'); }\n"
        php_code += f"$_combined = {' . '.join(var_names)};\n"
        php_code += "$_base64_decoded = base64_decode($_combined);\n"
        
        # XOR decryption
        php_code += "$_xor_decrypted = '';\n"
        php_code += "for ($_i = 0; $_i < strlen($_base64_decoded); $_i++) {\n"
        php_code += "    $_xor_decrypted .= chr(ord($_base64_decoded[$_i]) ^ ord($_key[$_i % strlen($_key)]));\n"
        php_code += "}\n"
        
        php_code += "$_final_decoded = base64_decode($_xor_decrypted);\n"
        php_code += "$_utf8_content = mb_convert_encoding($_final_decoded, 'UTF-8', 'auto');\n"
        php_code += "if (!mb_check_encoding($_utf8_content, 'UTF-8')) { die('UTF-8 validation failed'); }\n"
        php_code += "eval($_utf8_content);\n"
        php_code += "?>"
        
        return php_code

def main():
    """Main function for command-line usage."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Military-Grade UTF-8 Encryption Engine')
    parser.add_argument('input_file', help='Input file to encrypt')
    parser.add_argument('-o', '--output', help='Output file path')
    parser.add_argument('-l', '--level', choices=['basic', 'standard', 'advanced', 'maximum'], 
                       default='advanced', help='Encryption level')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.input_file):
        print(f"❌ Input file not found: {args.input_file}")
        return
    
    engine = MilitaryUTF8EncryptionEngine()
    success = engine.encrypt_file_with_utf8_support(args.input_file, args.output, args.level)
    
    if success:
        print("🎉 Encryption completed successfully!")
    else:
        print("💥 Encryption failed!")

if __name__ == '__main__':
    main()
