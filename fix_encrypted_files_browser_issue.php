<?php
/**
 * Fix Browser Execution Issue for Encrypted Laravel Files
 * This script fixes the web server configuration issue causing encrypted files to display as text
 */

header('Content-Type: text/html; charset=UTF-8');

// List of encrypted files
$encrypted_files = [
    'encrypted_utf8_laravel_db_migrate_tool.php',
    'encrypted_utf8_laravel_db_migrate.php',
    'encrypted_utf8_laravel_db_restore.php',
    'encrypted_utf8_laravel_developer_toolkit.php',
    'encrypted_utf8_laravel_npm_build.php',
    'encrypted_utf8_laravel_permissions_fixer.php',
    'encrypted_utf8_laravel_prod_error_fixer.php',
    'encrypted_utf8_laravel_run_artisan.php',
    'encrypted_utf8_laravel_symlink_creator.php'
];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'fix_permissions') {
        $results = [];
        
        foreach ($encrypted_files as $file) {
            if (file_exists($file)) {
                if (chmod($file, 0644)) {
                    $results[] = "✅ Fixed permissions for: $file";
                } else {
                    $results[] = "❌ Failed to fix permissions for: $file";
                }
            } else {
                $results[] = "⚠️ File not found: $file";
            }
        }
        
        echo json_encode(['success' => true, 'results' => $results]);
        exit;
    }
    
    if ($action === 'create_htaccess') {
        $htaccess_content = "# Force PHP execution for encrypted files
AddType application/x-httpd-php .php
php_flag display_errors Off
php_flag log_errors On

# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection \"1; mode=block\"

# Prevent direct access to sensitive files
<Files \"*.log\">
    Order allow,deny
    Deny from all
</Files>

# Enable PHP execution
<IfModule mod_php7.c>
    php_flag engine on
</IfModule>

<IfModule mod_php8.c>
    php_flag engine on
</IfModule>";

        if (file_put_contents('.htaccess', $htaccess_content)) {
            echo json_encode(['success' => true, 'message' => '✅ .htaccess file created successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => '❌ Failed to create .htaccess file']);
        }
        exit;
    }
    
    if ($action === 'test_file') {
        $filename = $_POST['filename'] ?? '';
        
        if (in_array($filename, $encrypted_files) && file_exists($filename)) {
            // Test if file can be executed
            ob_start();
            $error = '';
            
            try {
                // Capture any output
                include $filename;
                $output = ob_get_contents();
                ob_end_clean();
                
                // Check if we got HTML output (successful execution)
                if (strpos($output, '<!DOCTYPE html') !== false || strpos($output, '<html') !== false) {
                    echo json_encode([
                        'success' => true, 
                        'message' => '✅ File executes correctly and produces HTML output',
                        'output_preview' => substr($output, 0, 200) . '...'
                    ]);
                } else {
                    echo json_encode([
                        'success' => true, 
                        'message' => '✅ File executes without errors',
                        'output_preview' => substr($output, 0, 200) . '...'
                    ]);
                }
            } catch (Exception $e) {
                ob_end_clean();
                echo json_encode([
                    'success' => false, 
                    'message' => '❌ Execution error: ' . $e->getMessage()
                ]);
            }
        } else {
            echo json_encode(['success' => false, 'message' => '❌ File not found or invalid']);
        }
        exit;
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Encrypted Files Browser Issue</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #ff2d20, #ff6b35);
            color: white;
            border-radius: 10px;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
        }
        .section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .section h2 {
            color: #333;
            margin-top: 0;
        }
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: transform 0.2s;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .btn-success {
            background: linear-gradient(135deg, #28a745, #20c997);
        }
        .btn-danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
        }
        .btn-warning {
            background: linear-gradient(135deg, #ffc107, #ff8f00);
        }
        .results {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .file-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .file-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #ddd;
        }
        .file-card h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-success { background: #d4edda; color: #155724; }
        .status-error { background: #f8d7da; color: #721c24; }
        .status-warning { background: #fff3cd; color: #856404; }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Fix Encrypted Files Browser Issue</h1>
            <p>Resolve web server configuration issues for encrypted Laravel files</p>
        </div>

        <div class="section">
            <h2>🔍 Issue Diagnosis</h2>
            <p><strong>Problem:</strong> Encrypted files display obfuscated code instead of executing in browser.</p>
            <p><strong>Cause:</strong> Web server configuration issue - files being served as text instead of PHP.</p>
            <p><strong>Solution:</strong> Fix file permissions and web server configuration.</p>
        </div>

        <div class="section">
            <h2>🛠️ Quick Fixes</h2>
            
            <div style="margin: 20px 0;">
                <button class="btn btn-success" onclick="fixPermissions()">
                    🔒 Fix File Permissions
                </button>
                <button class="btn btn-warning" onclick="createHtaccess()">
                    📄 Create .htaccess Fix
                </button>
            </div>

            <div id="fix-results" class="results" style="display: none;"></div>
        </div>

        <div class="section">
            <h2>🧪 Test Encrypted Files</h2>
            <p>Test each encrypted file to verify it executes correctly:</p>
            
            <div class="file-grid">
                <?php foreach ($encrypted_files as $file): ?>
                    <div class="file-card">
                        <h4><?php echo htmlspecialchars($file); ?></h4>
                        <div style="margin: 10px 0;">
                            <?php if (file_exists($file)): ?>
                                <span class="status status-success">✅ File Exists</span>
                                <span class="status status-warning">📊 <?php echo number_format(filesize($file)); ?> bytes</span>
                            <?php else: ?>
                                <span class="status status-error">❌ File Missing</span>
                            <?php endif; ?>
                        </div>
                        <div>
                            <button class="btn" onclick="testFile('<?php echo $file; ?>')">
                                🧪 Test Execution
                            </button>
                            <a href="<?php echo $file; ?>" target="_blank" class="btn btn-warning">
                                🌐 Open in Browser
                            </a>
                        </div>
                        <div id="test-result-<?php echo md5($file); ?>" style="margin-top: 10px; display: none;"></div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <div class="section">
            <h2>📋 Manual Fix Instructions</h2>
            <p>If automatic fixes don't work, try these manual steps:</p>
            
            <h3>1. Check PHP Configuration</h3>
            <pre style="background: #f4f4f4; padding: 10px; border-radius: 4px;">
# Check if PHP is working
php -v

# Test file execution
php encrypted_utf8_laravel_developer_toolkit.php
            </pre>

            <h3>2. Web Server Configuration</h3>
            <p><strong>Apache:</strong> Add to .htaccess:</p>
            <pre style="background: #f4f4f4; padding: 10px; border-radius: 4px;">
AddType application/x-httpd-php .php
php_flag engine on
            </pre>

            <p><strong>Nginx:</strong> Ensure PHP-FPM is configured:</p>
            <pre style="background: #f4f4f4; padding: 10px; border-radius: 4px;">
location ~ \.php$ {
    fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
    fastcgi_index index.php;
    include fastcgi_params;
}
            </pre>

            <h3>3. File Permissions</h3>
            <pre style="background: #f4f4f4; padding: 10px; border-radius: 4px;">
chmod 644 encrypted_utf8_*.php
chown www-data:www-data encrypted_utf8_*.php
            </pre>
        </div>

        <div class="section">
            <h2>✅ Verification</h2>
            <p>After applying fixes, verify that:</p>
            <ul>
                <li>✅ Files have correct permissions (644)</li>
                <li>✅ .htaccess file is created</li>
                <li>✅ Web server is restarted</li>
                <li>✅ Files execute in browser instead of showing code</li>
            </ul>
        </div>
    </div>

    <script>
        function fixPermissions() {
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<div class="loading"></div> Fixing...';
            btn.disabled = true;

            fetch(window.location.href, {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: 'action=fix_permissions'
            })
            .then(response => response.json())
            .then(data => {
                const resultsDiv = document.getElementById('fix-results');
                resultsDiv.style.display = 'block';
                resultsDiv.innerHTML = '<h3>Permission Fix Results:</h3>' + 
                    data.results.map(result => `<div>${result}</div>`).join('');
            })
            .catch(error => {
                alert('Error: ' + error.message);
            })
            .finally(() => {
                btn.innerHTML = originalText;
                btn.disabled = false;
            });
        }

        function createHtaccess() {
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<div class="loading"></div> Creating...';
            btn.disabled = true;

            fetch(window.location.href, {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: 'action=create_htaccess'
            })
            .then(response => response.json())
            .then(data => {
                const resultsDiv = document.getElementById('fix-results');
                resultsDiv.style.display = 'block';
                resultsDiv.innerHTML = '<h3>.htaccess Creation Result:</h3><div>' + data.message + '</div>';
            })
            .catch(error => {
                alert('Error: ' + error.message);
            })
            .finally(() => {
                btn.innerHTML = originalText;
                btn.disabled = false;
            });
        }

        function testFile(filename) {
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<div class="loading"></div> Testing...';
            btn.disabled = true;

            const resultDiv = document.getElementById('test-result-' + btoa(filename).replace(/[^a-zA-Z0-9]/g, ''));

            fetch(window.location.href, {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: 'action=test_file&filename=' + encodeURIComponent(filename)
            })
            .then(response => response.json())
            .then(data => {
                resultDiv.style.display = 'block';
                resultDiv.innerHTML = `
                    <div class="status ${data.success ? 'status-success' : 'status-error'}">
                        ${data.message}
                    </div>
                    ${data.output_preview ? '<div style="margin-top: 5px; font-size: 12px; color: #666;">Preview: ' + data.output_preview + '</div>' : ''}
                `;
            })
            .catch(error => {
                resultDiv.style.display = 'block';
                resultDiv.innerHTML = '<div class="status status-error">❌ Test failed: ' + error.message + '</div>';
            })
            .finally(() => {
                btn.innerHTML = originalText;
                btn.disabled = false;
            });
        }
    </script>
</body>
</html>
