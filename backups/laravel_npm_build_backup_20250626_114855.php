<?php
/**
 * Laravel NPM Build Runner for cPanel Shared Hosting
 * 
 * This script allows you to run npm build commands on shared hosting
 * where terminal access is limited.
 * 
 * ⚠️ SECURITY WARNING: This script is potentially dangerous as it allows
 * executing commands. Use it only temporarily and delete immediately after use.
 * 
 * Usage: Upload this script to your public/scripts directory and access it via browser.
 */

// Set execution time limit to 600 seconds (10 minutes) as builds can take time
set_time_limit(600);

// Start output buffering
ob_start();

// Define the base directory (Lara<PERSON> root)
$baseDir = dirname(dirname(__DIR__));

// For debugging
echo '<div class="info">Base directory: ' . htmlspecialchars($baseDir) . '</div>';

// HTML header
echo '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Laravel NPM Build Runner</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2, h3 {
            color: #2d3748;
        }
        .success {
            color: #38a169;
            background-color: #f0fff4;
            border-left: 4px solid #38a169;
            padding: 8px 12px;
            margin: 8px 0;
        }
        .warning {
            color: #d69e2e;
            background-color: #fffaf0;
            border-left: 4px solid #d69e2e;
            padding: 8px 12px;
            margin: 8px 0;
        }
        .error {
            color: #e53e3e;
            background-color: #fff5f5;
            border-left: 4px solid #e53e3e;
            padding: 8px 12px;
            margin: 8px 0;
        }
        .info {
            color: #3182ce;
            background-color: #ebf8ff;
            border-left: 4px solid #3182ce;
            padding: 8px 12px;
            margin: 8px 0;
        }
        code {
            background-color: #f7fafc;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: Menlo, Monaco, Consolas, "Liberation Mono", monospace;
        }
        pre {
            background-color: #f7fafc;
            padding: 12px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .command-output {
            background-color: #1a202c;
            color: #e2e8f0;
            padding: 12px;
            border-radius: 5px;
            font-family: Menlo, Monaco, Consolas, "Liberation Mono", monospace;
            margin-top: 10px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1rem;
        }
        .card {
            border: 1px solid #e2e8f0;
            border-radius: 5px;
            padding: 1rem;
        }
        .card h3 {
            margin-top: 0;
        }
        form {
            margin-bottom: 20px;
        }
        input[type="text"], select {
            width: 100%;
            padding: 8px;
            margin-bottom: 10px;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
        }
        button {
            background-color: #4299e1;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background-color: #3182ce;
        }
        .security-warning {
            background-color: #742a2a;
            color: white;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-weight: bold;
        }
        .progress-container {
            width: 100%;
            background-color: #e2e8f0;
            border-radius: 5px;
            margin: 10px 0;
        }
        .progress-bar {
            height: 20px;
            background-color: #4299e1;
            border-radius: 5px;
            width: 0%;
            transition: width 0.3s;
        }
    </style>
</head>
<body>
    <div class="security-warning">
        ⚠️ SECURITY WARNING: This script is potentially dangerous as it allows executing commands.
        Delete this file immediately after use!
    </div>
    <h1>Laravel NPM Build Runner</h1>
';

// Check if Node.js and NPM are available
$nodeInstalled = false;
$npmInstalled = false;
$nodeVersion = '';
$npmVersion = '';
$laravelVersion = '';

// Check Node.js
exec('which node 2>&1', $nodeOutput, $nodeReturnVar);
if ($nodeReturnVar === 0) {
    $nodeInstalled = true;
    exec('node -v 2>&1', $nodeVersionOutput);
    $nodeVersion = !empty($nodeVersionOutput) ? $nodeVersionOutput[0] : 'Unknown';
}

// Check NPM
exec('which npm 2>&1', $npmOutput, $npmReturnVar);
if ($npmReturnVar === 0) {
    $npmInstalled = true;
    exec('npm -v 2>&1', $npmVersionOutput);
    $npmVersion = !empty($npmVersionOutput) ? $npmVersionOutput[0] : 'Unknown';
}

// Try to get Laravel version
try {
    // Check if composer.json exists and read it
    $composerJsonPath = $baseDir . '/composer.json';
    if (file_exists($composerJsonPath)) {
        $composerJson = json_decode(file_get_contents($composerJsonPath), true);
        if (isset($composerJson['require']['laravel/framework'])) {
            $laravelRequireVersion = $composerJson['require']['laravel/framework'];
            // Clean up version string (e.g., "^10.0" => "10.0")
            $laravelVersion = preg_replace('/[^0-9.]/', '', $laravelRequireVersion);
        }
    }
    
    // If we couldn't get the version from composer.json, try using the Application class
    if (empty($laravelVersion) && file_exists($baseDir . '/vendor/autoload.php')) {
        require_once $baseDir . '/vendor/autoload.php';
        
        if (class_exists('Illuminate\Foundation\Application')) {
            $laravelVersion = \Illuminate\Foundation\Application::VERSION;
        }
    }
} catch (\Exception $e) {
    $laravelVersion = 'Unknown (Error: ' . $e->getMessage() . ')';
}

// Display environment information
echo '<h2>Environment</h2>';
echo '<div class="grid">';

echo '<div class="card">';
echo '<h3>Node.js</h3>';
if ($nodeInstalled) {
    echo '<div class="success">Node.js is installed: ' . htmlspecialchars($nodeVersion) . '</div>';
} else {
    echo '<div class="error">Node.js is not installed or not in PATH</div>';
}
echo '</div>';

echo '<div class="card">';
echo '<h3>NPM</h3>';
if ($npmInstalled) {
    echo '<div class="success">NPM is installed: ' . htmlspecialchars($npmVersion) . '</div>';
} else {
    echo '<div class="error">NPM is not installed or not in PATH</div>';
}
echo '</div>';

echo '<div class="card">';
echo '<h3>Laravel</h3>';
if (!empty($laravelVersion)) {
    echo '<div class="success">Laravel version: ' . htmlspecialchars($laravelVersion) . '</div>';
} else {
    echo '<div class="warning">Laravel version could not be determined</div>';
}
echo '</div>';

echo '</div>';

// Check if we should run npm commands
if (isset($_POST['action'])) {
    $action = $_POST['action'];
    
    // Change to the base directory
    chdir($baseDir);
    
    echo '<h2>Executing Command</h2>';
    echo '<div class="command-output">';
    
    // Start time for performance tracking
    $startTime = microtime(true);
    
    if ($action === 'npm_install') {
        echo "Running: npm install\n\n";
        flush();
        
        // Execute npm install
        $descriptorSpec = [
            0 => ["pipe", "r"],  // stdin
            1 => ["pipe", "w"],  // stdout
            2 => ["pipe", "w"]   // stderr
        ];
        
        $process = proc_open('npm install', $descriptorSpec, $pipes, $baseDir);
        
        if (is_resource($process)) {
            // Close stdin
            fclose($pipes[0]);
            
            // Read output in real-time
            while (!feof($pipes[1])) {
                $output = fgets($pipes[1]);
                if ($output) {
                    echo htmlspecialchars($output);
                    flush();
                }
            }
            
            // Read error output
            while (!feof($pipes[2])) {
                $error = fgets($pipes[2]);
                if ($error) {
                    echo '<span style="color: #f56565;">' . htmlspecialchars($error) . '</span>';
                    flush();
                }
            }
            
            // Close pipes
            fclose($pipes[1]);
            fclose($pipes[2]);
            
            // Get exit code
            $exitCode = proc_close($process);
            
            if ($exitCode === 0) {
                echo "\n\n" . '<span style="color: #68d391;">npm install completed successfully!</span>';
            } else {
                echo "\n\n" . '<span style="color: #f56565;">npm install failed with exit code: ' . $exitCode . '</span>';
            }
        } else {
            echo '<span style="color: #f56565;">Failed to start the process</span>';
        }
    } else if ($action === 'npm_install_legacy') {
        echo "Running: npm install --legacy-peer-deps\n\n";
        flush();
        
        // Execute npm install with legacy-peer-deps
        $descriptorSpec = [
            0 => ["pipe", "r"],  // stdin
            1 => ["pipe", "w"],  // stdout
            2 => ["pipe", "w"]   // stderr
        ];
        
        $process = proc_open('npm install --legacy-peer-deps', $descriptorSpec, $pipes, $baseDir);
        
        if (is_resource($process)) {
            // Close stdin
            fclose($pipes[0]);
            
            // Read output in real-time
            while (!feof($pipes[1])) {
                $output = fgets($pipes[1]);
                if ($output) {
                    echo htmlspecialchars($output);
                    flush();
                }
            }
            
            // Read error output
            while (!feof($pipes[2])) {
                $error = fgets($pipes[2]);
                if ($error) {
                    echo '<span style="color: #f56565;">' . htmlspecialchars($error) . '</span>';
                    flush();
                }
            }
            
            // Close pipes
            fclose($pipes[1]);
            fclose($pipes[2]);
            
            // Get exit code
            $exitCode = proc_close($process);
            
            if ($exitCode === 0) {
                echo "\n\n" . '<span style="color: #68d391;">npm install --legacy-peer-deps completed successfully!</span>';
            } else {
                echo "\n\n" . '<span style="color: #f56565;">npm install --legacy-peer-deps failed with exit code: ' . $exitCode . '</span>';
            }
        } else {
            echo '<span style="color: #f56565;">Failed to start the process</span>';
        }
    } else if ($action === 'npm_build') {
        echo "Running: npm run build\n\n";
        flush();
        
        // Execute npm run build
        $descriptorSpec = [
            0 => ["pipe", "r"],  // stdin
            1 => ["pipe", "w"],  // stdout
            2 => ["pipe", "w"]   // stderr
        ];
        
        $process = proc_open('npm run build', $descriptorSpec, $pipes, $baseDir);
        
        if (is_resource($process)) {
            // Close stdin
            fclose($pipes[0]);
            
            // Read output in real-time
            while (!feof($pipes[1])) {
                $output = fgets($pipes[1]);
                if ($output) {
                    echo htmlspecialchars($output);
                    flush();
                }
            }
            
            // Read error output
            while (!feof($pipes[2])) {
                $error = fgets($pipes[2]);
                if ($error) {
                    echo '<span style="color: #f56565;">' . htmlspecialchars($error) . '</span>';
                    flush();
                }
            }
            
            // Close pipes
            fclose($pipes[1]);
            fclose($pipes[2]);
            
            // Get exit code
            $exitCode = proc_close($process);
            
            if ($exitCode === 0) {
                echo "\n\n" . '<span style="color: #68d391;">npm run build completed successfully!</span>';
            } else {
                echo "\n\n" . '<span style="color: #f56565;">npm run build failed with exit code: ' . $exitCode . '</span>';
            }
        } else {
            echo '<span style="color: #f56565;">Failed to start the process</span>';
        }
    } else if ($action === 'npm_dev') {
        echo "Running: npm run dev\n\n";
        flush();
        
        // Execute npm run dev
        $descriptorSpec = [
            0 => ["pipe", "r"],  // stdin
            1 => ["pipe", "w"],  // stdout
            2 => ["pipe", "w"]   // stderr
        ];
        
        $process = proc_open('npm run dev', $descriptorSpec, $pipes, $baseDir);
        
        if (is_resource($process)) {
            // Close stdin
            fclose($pipes[0]);
            
            // Read output in real-time
            while (!feof($pipes[1])) {
                $output = fgets($pipes[1]);
                if ($output) {
                    echo htmlspecialchars($output);
                    flush();
                }
            }
            
            // Read error output
            while (!feof($pipes[2])) {
                $error = fgets($pipes[2]);
                if ($error) {
                    echo '<span style="color: #f56565;">' . htmlspecialchars($error) . '</span>';
                    flush();
                }
            }
            
            // Close pipes
            fclose($pipes[1]);
            fclose($pipes[2]);
            
            // Get exit code
            $exitCode = proc_close($process);
            
            if ($exitCode === 0) {
                echo "\n\n" . '<span style="color: #68d391;">npm run dev completed successfully!</span>';
            } else {
                echo "\n\n" . '<span style="color: #f56565;">npm run dev failed with exit code: ' . $exitCode . '</span>';
            }
        } else {
            echo '<span style="color: #f56565;">Failed to start the process</span>';
        }
    } else if ($action === 'clear_cache') {
        echo "Running Laravel cache clear commands\n\n";
        flush();
        
        // Require the autoloader
        require $baseDir . '/vendor/autoload.php';
        
        try {
            // Load the environment file
            $dotenv = Dotenv\Dotenv::createImmutable($baseDir);
            $dotenv->load();
            
            // Bootstrap Laravel
            $app = require_once $baseDir . '/bootstrap/app.php';
            $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
            $kernel->bootstrap();
            
            // Run cache clear commands
            $commands = [
                'cache:clear' => 'Clearing application cache',
                'config:clear' => 'Clearing config cache',
                'route:clear' => 'Clearing route cache',
                'view:clear' => 'Clearing view cache',
                'optimize:clear' => 'Clearing optimized files',
            ];
            
            foreach ($commands as $command => $description) {
                echo "$description...\n";
                flush();
                
                $exitCode = \Illuminate\Support\Facades\Artisan::call($command);
                $output = \Illuminate\Support\Facades\Artisan::output();
                
                echo htmlspecialchars($output);
                
                if ($exitCode === 0) {
                    echo '<span style="color: #68d391;">Success!</span>' . "\n\n";
                } else {
                    echo '<span style="color: #f56565;">Failed with exit code: ' . $exitCode . '</span>' . "\n\n";
                }
                
                flush();
            }
            
            echo "\n" . '<span style="color: #68d391;">All cache clear commands completed!</span>';
        } catch (\Exception $e) {
            echo '<span style="color: #f56565;">Error: ' . htmlspecialchars($e->getMessage()) . '</span>';
        }
    } else if ($action === 'full_build') {
        echo "Running full build process (clear cache + npm install + npm run build)\n\n";
        echo "Step 1: Clearing Laravel cache\n";
        flush();
        
        // Require the autoloader
        require $baseDir . '/vendor/autoload.php';
        
        try {
            // Load the environment file
            $dotenv = Dotenv\Dotenv::createImmutable($baseDir);
            $dotenv->load();
            
            // Bootstrap Laravel
            $app = require_once $baseDir . '/bootstrap/app.php';
            $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
            $kernel->bootstrap();
            
            // Run cache clear commands
            $commands = [
                'cache:clear' => 'Clearing application cache',
                'config:clear' => 'Clearing config cache',
                'route:clear' => 'Clearing route cache',
                'view:clear' => 'Clearing view cache',
                'optimize:clear' => 'Clearing optimized files',
            ];
            
            foreach ($commands as $command => $description) {
                echo "$description...\n";
                flush();
                
                $exitCode = \Illuminate\Support\Facades\Artisan::call($command);
                $output = \Illuminate\Support\Facades\Artisan::output();
                
                echo htmlspecialchars($output);
                
                if ($exitCode === 0) {
                    echo '<span style="color: #68d391;">Success!</span>' . "\n\n";
                } else {
                    echo '<span style="color: #f56565;">Failed with exit code: ' . $exitCode . '</span>' . "\n\n";
                }
                
                flush();
            }
            
            echo "\n" . '<span style="color: #68d391;">All cache clear commands completed!</span>' . "\n\n";
            
            // Step 2: Run npm install
            echo "Step 2: Running npm install\n";
            flush();
            
            // Execute npm install
            $descriptorSpec = [
                0 => ["pipe", "r"],  // stdin
                1 => ["pipe", "w"],  // stdout
                2 => ["pipe", "w"]   // stderr
            ];
            
            $process = proc_open('npm install', $descriptorSpec, $pipes, $baseDir);
            
            if (is_resource($process)) {
                // Close stdin
                fclose($pipes[0]);
                
                // Read output in real-time
                while (!feof($pipes[1])) {
                    $output = fgets($pipes[1]);
                    if ($output) {
                        echo htmlspecialchars($output);
                        flush();
                    }
                }
                
                // Read error output
                while (!feof($pipes[2])) {
                    $error = fgets($pipes[2]);
                    if ($error) {
                        echo '<span style="color: #f56565;">' . htmlspecialchars($error) . '</span>';
                        flush();
                    }
                }
                
                // Close pipes
                fclose($pipes[1]);
                fclose($pipes[2]);
                
                // Get exit code
                $npmExitCode = proc_close($process);
                
                if ($npmExitCode === 0) {
                    echo "\n\n" . '<span style="color: #68d391;">npm install completed successfully!</span>' . "\n\n";
                } else {
                    echo "\n\n" . '<span style="color: #f56565;">npm install failed with exit code: ' . $npmExitCode . '</span>' . "\n\n";
                    
                    // If npm install failed, try with --legacy-peer-deps
                    echo "Retrying with --legacy-peer-deps...\n";
                    flush();
                    
                    $process = proc_open('npm install --legacy-peer-deps', $descriptorSpec, $pipes, $baseDir);
                    
                    if (is_resource($process)) {
                        // Close stdin
                        fclose($pipes[0]);
                        
                        // Read output in real-time
                        while (!feof($pipes[1])) {
                            $output = fgets($pipes[1]);
                            if ($output) {
                                echo htmlspecialchars($output);
                                flush();
                            }
                        }
                        
                        // Read error output
                        while (!feof($pipes[2])) {
                            $error = fgets($pipes[2]);
                            if ($error) {
                                echo '<span style="color: #f56565;">' . htmlspecialchars($error) . '</span>';
                                flush();
                            }
                        }
                        
                        // Close pipes
                        fclose($pipes[1]);
                        fclose($pipes[2]);
                        
                        // Get exit code
                        $npmExitCode = proc_close($process);
                        
                        if ($npmExitCode === 0) {
                            echo "\n\n" . '<span style="color: #68d391;">npm install --legacy-peer-deps completed successfully!</span>' . "\n\n";
                        } else {
                            echo "\n\n" . '<span style="color: #f56565;">npm install --legacy-peer-deps failed with exit code: ' . $npmExitCode . '</span>' . "\n\n";
                        }
                    } else {
                        echo '<span style="color: #f56565;">Failed to start the npm install --legacy-peer-deps process</span>' . "\n\n";
                    }
                }
            } else {
                echo '<span style="color: #f56565;">Failed to start the npm install process</span>' . "\n\n";
            }
            
            // Step 3: Run npm run build
            echo "Step 3: Running npm run build\n";
            flush();
            
            // Execute npm run build
            $process = proc_open('npm run build', $descriptorSpec, $pipes, $baseDir);
            
            if (is_resource($process)) {
                // Close stdin
                fclose($pipes[0]);
                
                // Read output in real-time
                while (!feof($pipes[1])) {
                    $output = fgets($pipes[1]);
                    if ($output) {
                        echo htmlspecialchars($output);
                        flush();
                    }
                }
                
                // Read error output
                while (!feof($pipes[2])) {
                    $error = fgets($pipes[2]);
                    if ($error) {
                        echo '<span style="color: #f56565;">' . htmlspecialchars($error) . '</span>';
                        flush();
                    }
                }
                
                // Close pipes
                fclose($pipes[1]);
                fclose($pipes[2]);
                
                // Get exit code
                $exitCode = proc_close($process);
                
                if ($exitCode === 0) {
                    echo "\n\n" . '<span style="color: #68d391;">npm run build completed successfully!</span>';
                } else {
                    echo "\n\n" . '<span style="color: #f56565;">npm run build failed with exit code: ' . $exitCode . '</span>';
                }
            } else {
                echo '<span style="color: #f56565;">Failed to start the npm run build process</span>';
            }
        } catch (\Exception $e) {
            echo '<span style="color: #f56565;">Error: ' . htmlspecialchars($e->getMessage()) . '</span>';
        }
    }
    
    // End time for performance tracking
    $endTime = microtime(true);
    $executionTime = round($endTime - $startTime, 2);
    
    echo "\n\n" . '<span style="color: #a0aec0;">Execution time: ' . $executionTime . ' seconds</span>';
    echo '</div>';
}

// Display available actions
echo '<h2>Available Actions</h2>';
echo '<div class="info">Select an action to perform:</div>';

echo '<div class="grid">';

// Clear Cache
echo '<div class="card">';
echo '<h3>Clear Laravel Cache</h3>';
echo '<p>Clear all Laravel cache (config, route, view, application)</p>';
echo '<form method="post">';
echo '<input type="hidden" name="action" value="clear_cache">';
echo '<button type="submit">Clear Cache</button>';
echo '</form>';
echo '</div>';

// NPM Install
echo '<div class="card">';
echo '<h3>NPM Install</h3>';
echo '<p>Install NPM dependencies</p>';
echo '<form method="post">';
echo '<input type="hidden" name="action" value="npm_install">';
echo '<button type="submit">Run npm install</button>';
echo '</form>';
echo '</div>';

// NPM Install with legacy-peer-deps
echo '<div class="card">';
echo '<h3>NPM Install with legacy-peer-deps</h3>';
echo '<p>Install NPM dependencies with --legacy-peer-deps flag</p>';
echo '<form method="post">';
echo '<input type="hidden" name="action" value="npm_install_legacy">';
echo '<button type="submit">Run npm install --legacy-peer-deps</button>';
echo '</form>';
echo '</div>';

// NPM Build
echo '<div class="card">';
echo '<h3>NPM Build</h3>';
echo '<p>Build assets for production</p>';
echo '<form method="post">';
echo '<input type="hidden" name="action" value="npm_build">';
echo '<button type="submit">Run npm run build</button>';
echo '</form>';
echo '</div>';

// NPM Dev
echo '<div class="card">';
echo '<h3>NPM Dev</h3>';
echo '<p>Build assets for development</p>';
echo '<form method="post">';
echo '<input type="hidden" name="action" value="npm_dev">';
echo '<button type="submit">Run npm run dev</button>';
echo '</form>';
echo '</div>';

// Full Build Process
echo '<div class="card">';
echo '<h3>Full Build Process</h3>';
echo '<p>Clear cache + npm install + npm run build</p>';
echo '<form method="post">';
echo '<input type="hidden" name="action" value="full_build">';
echo '<button type="submit">Run Full Build</button>';
echo '</form>';
echo '</div>';

echo '</div>';

// Security notice
echo '
<div class="info" style="margin-top: 20px;">
    <h3>Security Notice</h3>
    <p>⚠️ This script provides direct access to run commands on your server. For security:</p>
    <ul>
        <li>Delete this file immediately after use</li>
        <li>Consider password-protecting this directory</li>
        <li>Never leave this script on a production server permanently</li>
    </ul>
</div>

</body>
</html>
'; 