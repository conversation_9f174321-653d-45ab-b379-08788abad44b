<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Laravel Developer Toolkit</title>
    <style>
        :root {
            --primary: #ff2d20;
            --primary-dark: #e02417;
            --secondary: #2d3748;
            --secondary-light: #4a5568;
            --accent: #667eea;
            --success: #48bb78;
            --warning: #ed8936;
            --danger: #f56565;
            --info: #4299e1;
            --light: #f7fafc;
            --dark: #1a202c;
            --text: #2d3748;
            --text-light: #718096;
            --border: #e2e8f0;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: var(--text);
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            border-radius: 20px;
            padding: 25px 40px;
            margin-bottom: 30px;
            box-shadow: var(--shadow-lg);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, var(--primary) 0%, var(--accent) 100%);
        }

        .logo {
            width: 300px;
            height: auto;
            margin: 0 auto 15px;
            filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text);
            margin-bottom: 8px;
        }

        .header p {
            font-size: 1.1rem;
            color: var(--text-light);
            max-width: 600px;
            margin: 0 auto;
        }

        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .tool-card {
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
            border: 1px solid var(--border);
            position: relative;
            overflow: hidden;
        }

        .tool-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .tool-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }

        .tool-card:hover::before {
            transform: scaleX(1);
        }

        .tool-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
        }

        .tool-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.3rem;
            color: white;
            flex-shrink: 0;
        }

        .tool-title {
            font-size: 1.4rem;
            font-weight: 600;
            color: var(--text);
            margin: 0;
        }

        .tool-description {
            color: var(--text-light);
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .tool-link {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .tool-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(255, 45, 32, 0.3);
        }

        .stats-section {
            background: white;
            border-radius: 16px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: var(--shadow);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 20px;
            border-radius: 12px;
            background: var(--light);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 5px;
        }

        .stat-label {
            color: var(--text-light);
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .creator-section {
            background: white;
            color: var(--text);
            border-radius: 16px;
            padding: 40px;
            box-shadow: var(--shadow-lg);
            margin-bottom: 30px;
        }

        .creator-title {
            text-align: center;
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 30px;
            color: var(--text);
        }

        .creator-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .creator-item {
            background: var(--light);
            padding: 20px;
            border-radius: 12px;
            transition: all 0.3s ease;
            border: 1px solid var(--border);
        }

        .creator-item:hover {
            background: #f1f5f9;
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        .creator-label {
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 5px;
        }

        .creator-value {
            color: var(--text-light);
        }

        .creator-value a {
            color: var(--text-light);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .creator-value a:hover {
            color: var(--primary);
        }

        .support-section {
            background: white;
            border-radius: 16px;
            padding: 40px;
            box-shadow: var(--shadow-lg);
            text-align: center;
            grid-column: span 2;
        }

        .support-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--text);
        }

        .support-description {
            color: var(--text-light);
            margin-bottom: 15px;
            font-size: 1.05rem;
        }

        .crypto-encourage {
            color: #f59e0b;
            font-weight: 500;
            margin-bottom: 15px !important;
            padding: 8px 12px;
            background: linear-gradient(135deg, #fef3c7, #fde68a);
            border-radius: 6px;
            border-left: 3px solid #f59e0b;
            font-size: 0.9rem;
        }

        .support-buttons {
            display: flex;
            gap: 12px;
            justify-content: center;
            flex-wrap: nowrap;
            overflow-x: auto;
            padding: 0 10px;
        }

        .support-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: linear-gradient(135deg, var(--warning) 0%, #d69e2e 100%);
            color: white;
            text-decoration: none;
            padding: 14px 18px;
            border-radius: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            white-space: nowrap;
            flex: 1;
            min-width: 0;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .support-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(237, 137, 54, 0.4);
        }

        .support-btn.paypal {
            background: linear-gradient(135deg, #0070ba 0%, #003087 100%);
        }

        .support-btn.paypal:hover {
            box-shadow: 0 8px 25px rgba(0, 112, 186, 0.4);
        }

        .support-btn.googlepay {
            background: linear-gradient(135deg, #4285f4 0%, #1a73e8 100%);
        }

        .support-btn.googlepay:hover {
            box-shadow: 0 8px 25px rgba(66, 133, 244, 0.4);
        }

        .support-btn.binance {
            background: linear-gradient(135deg, #f3ba2f 0%, #f0b90b 100%);
        }

        .support-btn.binance:hover {
            box-shadow: 0 8px 25px rgba(243, 186, 47, 0.4);
        }

        .support-notification {
            position: absolute;
            top: 20px;
            right: 40px;
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            color: white;
            padding: 8px 16px;
            border-radius: 50px;
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-lg);
            z-index: 10;
            text-decoration: none;
            font-weight: 600;
            font-size: 0.85rem;
        }

        .support-notification:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 45, 32, 0.4);
            color: white;
            text-decoration: none;
        }

        .notification-bell {
            font-size: 1.2rem;
            animation: bellRing 2s ease-in-out infinite;
        }

        .support-notification::before,
        .support-notification::after {
            content: '';
            position: absolute;
            top: -4px;
            left: -4px;
            right: -4px;
            bottom: -4px;
            border-radius: 50px;
            border: 2px solid rgba(255, 45, 32, 0.6);
            opacity: 0;
            animation: edgeWaveAnimation 2.5s ease-out infinite;
            pointer-events: none;
        }

        .support-notification::after {
            animation-delay: 1.25s;
            border-color: rgba(255, 45, 32, 0.4);
        }

        @keyframes bellRing {
            0%, 50%, 100% { transform: rotate(0deg); }
            10%, 30% { transform: rotate(-10deg); }
            20%, 40% { transform: rotate(10deg); }
        }

        @keyframes edgeWaveAnimation {
            0% {
                opacity: 0;
                transform: scale(1);
            }
            50% {
                opacity: 0.8;
            }
            100% {
                opacity: 0;
                transform: scale(1.15);
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            .header {
                padding: 20px 15px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .tools-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .tool-card {
                padding: 25px;
            }

            .tool-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }

            .tool-icon {
                width: 45px;
                height: 45px;
                font-size: 1.2rem;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .creator-grid {
                grid-template-columns: 1fr;
            }

            .support-buttons {
                flex-direction: column;
                align-items: center;
            }

            .support-btn {
                width: 100%;
                max-width: 250px;
                justify-content: center;
            }

            .support-section {
                grid-column: span 1;
            }

            .support-notification {
                position: static;
                margin: 20px auto 0;
                display: inline-flex;
                font-size: 0.75rem;
                padding: 8px 14px;
                gap: 6px;
            }

            .support-notification::before,
            .support-notification::after {
                display: none;
            }

            .support-buttons {
                gap: 6px;
                padding: 0 5px;
            }

            .support-btn {
                padding: 12px 14px;
                font-size: 0.8rem;
                gap: 6px;
                min-height: 44px;
            }
        }

        @media (max-width: 480px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }

        .loading {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .fade-in {
            animation: fadeIn 0.6s ease-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header Section -->
        <div class="header fade-in">
            <div class="logo">
                <svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 934 345" style="enable-background:new 0 0 934 345;" xml:space="preserve">
                <g>
                    <g>
                        <polygon style="fill:#EF3B2D;" points="389.647,121.376 373.181,121.376 373.181,221.606 420.575,221.606 420.575,206.858 389.647,206.858"/>
                        <path style="fill:#EF3B2D;" d="M478.706,163.902c-2.101-3.341-5.083-5.965-8.949-7.875c-3.865-1.909-7.756-2.864-11.669-2.864c-5.062,0-9.69,0.931-13.89,2.792c-4.201,1.861-7.804,4.417-10.811,7.661c-3.007,3.246-5.347,6.993-7.016,11.239c-1.672,4.249-2.506,8.713-2.506,13.389c0,4.774,0.834,9.26,2.506,13.459c1.669,4.202,4.009,7.925,7.016,11.169c3.007,3.246,6.609,5.799,10.811,7.66c4.199,1.861,8.828,2.792,13.89,2.792c3.913,0,7.804-0.955,11.669-2.863c3.866-1.908,6.849-4.533,8.949-7.875v9.021h15.607v-66.725h-15.607V163.902z M477.275,196.405c-0.955,2.578-2.291,4.821-4.009,6.73c-1.719,1.91-3.795,3.437-6.229,4.582c-2.435,1.146-5.133,1.718-8.091,1.718c-2.96,0-5.633-0.572-8.019-1.718c-2.387-1.146-4.438-2.672-6.156-4.582c-1.719-1.909-3.032-4.152-3.938-6.73c-0.909-2.577-1.36-5.298-1.36-8.161c0-2.864,0.451-5.585,1.36-8.162c0.905-2.577,2.219-4.819,3.938-6.729c1.718-1.908,3.77-3.437,6.156-4.582c2.386-1.146,5.059-1.718,8.019-1.718c2.958,0,5.656,0.572,8.091,1.718c2.434,1.146,4.51,2.674,6.229,4.582c1.718,1.91,3.054,4.152,4.009,6.729c0.953,2.577,1.432,5.298,1.432,8.162C478.706,191.107,478.228,193.828,477.275,196.405z"/>
                        <path style="fill:#EF3B2D;" d="M605.569,163.902c-2.101-3.341-5.083-5.965-8.949-7.875c-3.865-1.909-7.756-2.864-11.669-2.864c-5.062,0-9.69,0.931-13.89,2.792c-4.201,1.861-7.804,4.417-10.811,7.661c-3.007,3.246-5.347,6.993-7.016,11.239c-1.672,4.249-2.506,8.713-2.506,13.389c0,4.774,0.834,9.26,2.506,13.459c1.669,4.202,4.009,7.925,7.016,11.169c3.007,3.246,6.609,5.799,10.811,7.66c4.199,1.861,8.828,2.792,13.89,2.792c3.913,0,7.804-0.955,11.669-2.863c3.866-1.908,6.849-4.533,8.949-7.875v9.021h15.607v-66.725h-15.607V163.902z M604.137,196.405c-0.955,2.578-2.291,4.821-4.009,6.73c-1.719,1.91-3.795,3.437-6.229,4.582c-2.435,1.146-5.133,1.718-8.091,1.718c-2.96,0-5.633-0.572-8.019-1.718c-2.387-1.146-4.438-2.672-6.156-4.582c-1.719-1.909-3.032-4.152-3.938-6.73c-0.909-2.577-1.36-5.298-1.36-8.161c0-2.864,0.451-5.585,1.36-8.162c0.905-2.577,2.219-4.819,3.938-6.729c1.718-1.908,3.77-3.437,6.156-4.582c2.386-1.146,5.059-1.718,8.019-1.718c2.958,0,5.656,0.572,8.091,1.718c2.434,1.146,4.51,2.674,6.229,4.582c1.718,1.91,3.054,4.152,4.009,6.729c0.953,2.577,1.432,5.298,1.432,8.162C605.569,191.107,605.09,193.828,604.137,196.405z"/>
                        <rect x="776.781" y="121.376" style="fill:#EF3B2D;" width="15.606" height="100.23"/>
                        <polygon style="fill:#EF3B2D;" points="506.628,221.606 522.235,221.606 522.235,170.238 549.011,170.238 549.011,154.882 506.628,154.882"/>
                        <polygon style="fill:#EF3B2D;" points="683.748,154.882 664.132,205.978 644.516,154.882 628.708,154.882 654.325,221.606 673.939,221.606 699.556,154.882"/>
                        <path style="fill:#EF3B2D;" d="M733.595,153.166c-19.112,0-34.239,15.706-34.239,35.079c0,21.416,14.641,35.079,36.239,35.079c12.088,0,19.806-4.622,29.234-14.688l-10.544-8.158c-0.006,0.008-7.958,10.449-19.832,10.449c-13.802,0-19.612-11.127-19.612-16.884h51.777C769.338,172,754.846,153.166,733.595,153.166z M714.882,182.446c0.12-1.284,1.917-16.884,18.589-16.884c16.671,0,18.697,15.598,18.813,16.884H714.882z"/>
                    </g>
                    <path style="fill:#EF3B2D;" d="M325.683,120.592c-0.024-0.088-0.073-0.165-0.104-0.25c-0.058-0.157-0.108-0.316-0.191-0.46c-0.056-0.097-0.137-0.176-0.203-0.265c-0.087-0.117-0.161-0.242-0.265-0.345c-0.085-0.086-0.194-0.148-0.29-0.223c-0.109-0.085-0.206-0.182-0.327-0.252c0,0-0.001,0-0.002-0.001c-0.001,0-0.001-0.001-0.002-0.002L288.651,98.27c-0.917-0.528-2.047-0.528-2.964,0l-35.647,20.522c-0.001,0-0.001,0.001-0.002,0.002c-0.001,0-0.002,0-0.002,0.001c-0.121,0.07-0.219,0.167-0.327,0.252c-0.096,0.075-0.205,0.138-0.29,0.223c-0.103,0.103-0.178,0.228-0.265,0.345c-0.066,0.089-0.147,0.169-0.203,0.265c-0.083,0.144-0.133,0.304-0.191,0.46c-0.031,0.085-0.08,0.162-0.104,0.25c-0.067,0.249-0.103,0.51-0.103,0.776v38.979l-29.706,17.103v-76.255c0-0.265-0.036-0.526-0.103-0.776c-0.024-0.088-0.073-0.165-0.104-0.25c-0.058-0.157-0.108-0.316-0.191-0.46c-0.056-0.097-0.137-0.176-0.203-0.265c-0.087-0.117-0.161-0.242-0.265-0.345c-0.085-0.086-0.194-0.148-0.29-0.223c-0.109-0.085-0.206-0.182-0.327-0.252c0,0-0.001,0-0.002-0.001c-0.001,0-0.001-0.001-0.002-0.002l-35.647-20.523c-0.917-0.528-2.047-0.528-2.964,0l-35.647,20.523c-0.001,0-0.001,0.001-0.002,0.002c-0.001,0-0.002,0-0.002,0.001c-0.121,0.07-0.219,0.167-0.327,0.252c-0.096,0.075-0.205,0.138-0.29,0.223c-0.103,0.103-0.178,0.228-0.265,0.345c-0.066,0.089-0.147,0.169-0.203,0.265c-0.083,0.144-0.133,0.304-0.191,0.46c-0.031,0.085-0.08,0.162-0.104,0.25c-0.067,0.249-0.103,0.51-0.103,0.776v122.09c0,1.063,0.568,2.044,1.489,2.575l71.293,41.045c0.156,0.089,0.324,0.143,0.49,0.202c0.078,0.028,0.15,0.074,0.23,0.095c0.249,0.066,0.506,0.1,0.762,0.1c0.256,0,0.512-0.033,0.762-0.1c0.069-0.018,0.132-0.059,0.2-0.083c0.176-0.061,0.354-0.119,0.519-0.214l71.293-41.045c0.921-0.53,1.489-1.512,1.489-2.575v-38.979l34.158-19.666c0.921-0.53,1.489-1.512,1.489-2.575v-40.697C325.786,121.102,325.751,120.841,325.683,120.592z M215.87,219.867l-29.648-16.779l31.136-17.926c0,0,0,0,0.001-0.001l34.164-19.669l29.674,17.084l-21.772,12.428L215.87,219.867z M284.199,143.608v33.841l-12.475-7.182l-17.231-9.92v-33.841l12.475,7.182L284.199,143.608z M287.169,104.273l29.693,17.095l-29.693,17.095l-29.693-17.095L287.169,104.273z M195.675,190.789l-12.475,7.182v-74.538l17.231-9.92l12.475-7.182v74.537L195.675,190.789z M180.229,84.098l29.693,17.095l-29.693,17.095l-29.693-17.095L180.229,84.098z M147.553,106.332l12.475,7.182l17.231,9.92v79.676c0,0.002,0.001,0.003,0.001,0.005s-0.001,0.004-0.001,0.006c0,0.114,0.032,0.221,0.045,0.333c0.017,0.146,0.021,0.294,0.059,0.434c0.001,0.002,0.001,0.005,0.002,0.007c0.032,0.117,0.094,0.222,0.14,0.334c0.051,0.124,0.088,0.255,0.156,0.371c0.001,0.003,0.002,0.006,0.004,0.009c0.061,0.105,0.149,0.191,0.222,0.288c0.081,0.105,0.149,0.22,0.244,0.314c0.003,0.003,0.005,0.007,0.008,0.01c0.084,0.083,0.19,0.142,0.284,0.215c0.106,0.083,0.202,0.178,0.32,0.247c0.004,0.002,0.009,0.003,0.013,0.005c0.004,0.002,0.007,0.006,0.011,0.008l34.139,19.321v34.175l-65.352-37.625V106.332z M284.199,221.567l-65.352,37.625v-34.182l48.399-27.628l16.953-9.677V221.567z M319.845,160.347l-29.706,17.102v-33.841l17.231-9.92l12.475-7.182V160.347z"/>
                </g>
                </svg>
            </div>
            <h1>Laravel Developer Toolkit</h1>
            <p>Comprehensive collection of Laravel development tools and utilities for streamlined project management, debugging, and deployment workflows.</p>

            <!-- Support Notification Bell -->
            <a href="#support-section" class="support-notification" id="supportNotification">
                <span class="notification-bell">🔔</span>
                <span>Support This Project</span>
            </a>
        </div>

        <!-- Statistics Section -->
        <div class="stats-section fade-in">
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">7</div>
                    <div class="stat-label">Development Tools</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">100%</div>
                    <div class="stat-label">Laravel Compatible</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">∞</div>
                    <div class="stat-label">Time Saved</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">24/7</div>
                    <div class="stat-label">Ready to Use</div>
                </div>
            </div>
        </div>

        <!-- Tools Grid -->
        <div class="tools-grid fade-in">
            <!-- Database Migration Tool -->
            <div class="tool-card">
                <div class="tool-header">
                    <div class="tool-icon">🗄️</div>
                    <h3 class="tool-title">Database Migration</h3>
                </div>
                <p class="tool-description">Run Laravel database migrations with real-time progress tracking and rollback capabilities. Perfect for managing database schema changes.</p>
                <a href="laravel_db_migrate.php" target="_blank" class="tool-link">
                    <span>Launch Tool</span>
                    <span>↗</span>
                </a>
            </div>

            <!-- Database Restore Tool -->
            <div class="tool-card">
                <div class="tool-header">
                    <div class="tool-icon">🔄</div>
                    <h3 class="tool-title">Database Restore</h3>
                </div>
                <p class="tool-description">Restore database backups with advanced options for selective restoration and data validation. Includes backup verification.</p>
                <a href="laravel_db_restore.php" target="_blank" class="tool-link">
                    <span>Launch Tool</span>
                    <span>↗</span>
                </a>
            </div>

            <!-- NPM Build Tool -->
            <div class="tool-card">
                <div class="tool-header">
                    <div class="tool-icon">📦</div>
                    <h3 class="tool-title">NPM Build Manager</h3>
                </div>
                <p class="tool-description">Manage NPM dependencies and build processes with automated optimization and deployment preparation features.</p>
                <a href="laravel_npm_build.php" target="_blank" class="tool-link">
                    <span>Launch Tool</span>
                    <span>↗</span>
                </a>
            </div>

            <!-- Permissions Fixer -->
            <div class="tool-card">
                <div class="tool-header">
                    <div class="tool-icon">🔐</div>
                    <h3 class="tool-title">Permissions Fixer</h3>
                </div>
                <p class="tool-description">Automatically fix Laravel file and directory permissions with security best practices and detailed permission analysis.</p>
                <a href="laravel_permissions_fixer.php" target="_blank" class="tool-link">
                    <span>Launch Tool</span>
                    <span>↗</span>
                </a>
            </div>

            <!-- Production Error Fixer -->
            <div class="tool-card">
                <div class="tool-header">
                    <div class="tool-icon">🚨</div>
                    <h3 class="tool-title">Production Error Fixer</h3>
                </div>
                <p class="tool-description">Diagnose and fix common Laravel production errors with automated solutions and comprehensive error analysis.</p>
                <a href="laravel_prod_error-fixer.php" target="_blank" class="tool-link">
                    <span>Launch Tool</span>
                    <span>↗</span>
                </a>
            </div>

            <!-- Artisan Command Runner -->
            <div class="tool-card">
                <div class="tool-header">
                    <div class="tool-icon">⚡</div>
                    <h3 class="tool-title">Artisan Command Runner</h3>
                </div>
                <p class="tool-description">Execute Laravel Artisan commands through a web interface with command history and output formatting.</p>
                <a href="laravel_run_artisan.php" target="_blank" class="tool-link">
                    <span>Launch Tool</span>
                    <span>↗</span>
                </a>
            </div>

            <!-- Storage Symlink Creator -->
            <div class="tool-card">
                <div class="tool-header">
                    <div class="tool-icon">🔗</div>
                    <h3 class="tool-title">Storage Symlink Creator</h3>
                </div>
                <p class="tool-description">Create and manage Laravel storage symbolic links with verification and testing capabilities for file access.</p>
                <a href="laravel_symlink_creator.php" target="_blank" class="tool-link">
                    <span>Launch Tool</span>
                    <span>↗</span>
                </a>
            </div>

            <!-- Support Section -->
            <div class="tool-card support-section" id="support-section">
                <h3 class="support-title">☕ Support This Project</h3>
                <p class="support-description">If these tools have saved you time and effort, consider supporting the development with a coffee!</p>
                <p class="support-description crypto-encourage">💰 Crypto users: Send any amount to the Binance ID below - every contribution helps keep these tools free!</p>
                <div class="support-buttons">
                    <a href="#" onclick="copyBinanceId(event)" class="support-btn binance">
                        <span>₿</span>
                        <span>ID: 780196125</span>
                    </a>
                    <a href="https://www.buymeacoffee.com/firozanam" target="_blank" class="support-btn">
                        <span>☕</span>
                        <span>Buy Me a Coffee</span>
                    </a>
                    <a href="https://www.paypal.me/firozanam" target="_blank" class="support-btn paypal">
                        <span>💳</span>
                        <span>PayPal Donation</span>
                    </a>
                    <a href="https://pay.google.com/gp/w/u/0/home/<USER>" target="_blank" class="support-btn googlepay">
                        <span>📱</span>
                        <span>Google Pay</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Creator Section -->
        <div class="creator-section fade-in">
            <h3 class="creator-title">👨‍💻 Created By</h3>
            <div class="creator-grid">
                <div class="creator-item">
                    <div class="creator-label">Full Stack Developer</div>
                    <div class="creator-value">Firoz Anam</div>
                </div>
                <div class="creator-item">
                    <div class="creator-label">📧 Email</div>
                    <div class="creator-value">
                        <a href="mailto:<EMAIL>"><EMAIL></a>
                    </div>
                </div>
                <div class="creator-item">
                    <div class="creator-label">🐙 GitHub</div>
                    <div class="creator-value">
                        <a href="https://github.com/firozanam" target="_blank">github.com/firozanam</a>
                    </div>
                </div>
                <div class="creator-item">
                    <div class="creator-label">💼 LinkedIn</div>
                    <div class="creator-value">
                        <a href="https://www.linkedin.com/in/firozanam" target="_blank">linkedin.com/in/firozanam</a>
                    </div>
                </div>
                <div class="creator-item">
                    <div class="creator-label">📱 WhatsApp</div>
                    <div class="creator-value">
                        <a href="https://wa.me/8801788544788" target="_blank">+8801788544788</a>
                    </div>
                </div>
                <div class="creator-item">
                    <div class="creator-label">🌐 Website</div>
                    <div class="creator-value">
                        <a href="https://neurotechsystem.com" target="_blank">neurotechsystem.com</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Add fade-in animation on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe all fade-in elements
        document.querySelectorAll('.fade-in').forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(20px)';
            el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(el);
        });

        // Add click tracking for analytics (optional)
        document.querySelectorAll('.tool-link').forEach(link => {
            link.addEventListener('click', function(e) {
                const toolName = this.closest('.tool-card').querySelector('.tool-title').textContent;
                console.log(`Tool accessed: ${toolName}`);

                // Optional: Send analytics data
                // gtag('event', 'tool_access', { tool_name: toolName });
            });
        });

        // Add loading state to links
        document.querySelectorAll('.tool-link').forEach(link => {
            link.addEventListener('click', function(e) {
                const originalContent = this.innerHTML;
                this.innerHTML = '<div class="loading"></div> Opening...';

                // Reset after a short delay
                setTimeout(() => {
                    this.innerHTML = originalContent;
                }, 2000);
            });
        });

        // Add keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                // Optional: Add escape key functionality
                console.log('Escape key pressed');
            }
        });

        // Smooth scroll to support section
        document.getElementById('supportNotification').addEventListener('click', function(e) {
            e.preventDefault();
            const supportSection = document.getElementById('support-section');
            if (supportSection) {
                supportSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });

                // Add a subtle highlight effect
                supportSection.style.transform = 'scale(1.02)';
                supportSection.style.transition = 'transform 0.3s ease';
                setTimeout(() => {
                    supportSection.style.transform = 'scale(1)';
                }, 600);
            }
        });

        // Copy Binance ID to clipboard
        function copyBinanceId(event) {
            event.preventDefault();
            const binanceId = '780196125';

            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(binanceId).then(function() {
                    // Show success message
                    const btn = event.target.closest('.support-btn');
                    const originalText = btn.innerHTML;
                    btn.innerHTML = '<span>✅</span><span>ID Copied!</span>';
                    btn.style.background = 'linear-gradient(135deg, #10b981 0%, #059669 100%)';

                    setTimeout(() => {
                        btn.innerHTML = originalText;
                        btn.style.background = 'linear-gradient(135deg, #f3ba2f 0%, #f0b90b 100%)';
                    }, 2000);
                }).catch(function(err) {
                    // Fallback for older browsers
                    console.log('Could not copy text: ', err);
                    alert('Binance ID: 780196125\n\nPlease copy this ID manually.');
                });
            } else {
                // Fallback for older browsers or non-secure context
                alert('Binance ID: 780196125\n\nPlease copy this ID manually.');
            }
        }

        // Add responsive grid adjustment
        function adjustGrid() {
            const grid = document.querySelector('.tools-grid');
            const cards = document.querySelectorAll('.tool-card');
            const containerWidth = grid.offsetWidth;

            if (containerWidth < 768) {
                grid.style.gridTemplateColumns = '1fr';
            } else if (containerWidth < 1200) {
                grid.style.gridTemplateColumns = 'repeat(2, 1fr)';
            } else {
                grid.style.gridTemplateColumns = 'repeat(auto-fit, minmax(350px, 1fr))';
            }
        }

        // Adjust grid on load and resize
        window.addEventListener('load', adjustGrid);
        window.addEventListener('resize', adjustGrid);

        // Add smooth scrolling for better UX
        document.documentElement.style.scrollBehavior = 'smooth';

        // Add tool card interaction effects
        document.querySelectorAll('.tool-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px) scale(1.02)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });

        // Add statistics counter animation
        function animateCounters() {
            const counters = document.querySelectorAll('.stat-number');

            counters.forEach(counter => {
                const target = counter.textContent;
                if (target === '∞' || target === '24/7' || target === '100%') return;

                const count = parseInt(target);
                let current = 0;
                const increment = count / 30;

                const timer = setInterval(() => {
                    current += increment;
                    if (current >= count) {
                        counter.textContent = count;
                        clearInterval(timer);
                    } else {
                        counter.textContent = Math.floor(current);
                    }
                }, 50);
            });
        }

        // Trigger counter animation when stats section is visible
        const statsObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateCounters();
                    statsObserver.unobserve(entry.target);
                }
            });
        });

        const statsSection = document.querySelector('.stats-section');
        if (statsSection) {
            statsObserver.observe(statsSection);
        }
    </script>
</body>
</html>
